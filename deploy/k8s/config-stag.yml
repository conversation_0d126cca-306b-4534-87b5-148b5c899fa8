apiVersion: v1
kind: ConfigMap
metadata:
  name: vclub-payment
  namespace: vinclub-backend-stag
data:
  JAVA_OPTS: "-Xmx1024m -Dlog4j.formatMsgNoLookups=true -javaagent:/usr/app/elastic-apm-agent.jar -Delastic.apm.service_name=vclub-payment-service -Delastic.apm.application_packages=com.vinloyalty,vn.vinclub -Delastic.apm.server_url=https://stag-apm.vinhomes.vn -Delastic.apm.secret_token=6NZOW0qPVsfVIdKv1oVZa0Gq -Delastic.apm.environment=stag"

  # PROFILER
  PROFILER_KEY: "VinClub@123!"

  # EXCHANGE RATE
  EXCHANGE_MONEY_UNIT_AMOUNT: "1000"
  POINT_PER_MONEY_UNIT_AMOUNT: "1"

  #  POSTGRESQL CONFIG
  POSTGRESQL_URL: "******************************************************************************************************************************************"
  DB_DEFAULT_SCHEMA: "payment_db"
  POSTGRESQL_USER: "vinclub_payment_user"
  POSTGRESQL_PASSWORD: "BQULvPBTdKFMPjlvrUBsBOT90G2U8Yfyv1FMAkKF0w"

  #  REDIS CONFIG
  REDISSON_CONFIG_HOST: "vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com"
  REDISSON_CONFIG_PORT: "6379"
  REDISSON_CONFIG_ADDRESS: 'redis://vhm-vinclub-stag-redis-azc.05cno1.0001.apse1.cache.amazonaws.com:6379'
  REDISSON_CONFIG_PASSWORD: ''
  REDISSON_CONFIG_DATABASE: '2'
  REDISSON_CONFIG_RESPONSE_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_TIMEOUT: '3000'
  REDISSON_CONFIG_CONNECTION_IDLE_TIME: '300000'
  REDISSON_CONFIG_CONNECTION_KEEP_ALIVE: 'true'
  REDISSON_CONFIG_CONNECTION_MAX: '64'
  REDISSON_CONFIG_CONNECTION_MIN: '4'
  REDISSON_CHANNEL_CHANGE: "CHANNEL.UPDATE"

  # KAFKA CONFIG
  KAFKA_BOOTSTRAP_SERVER: "b-2.vhmvinclubstagmsk.lggqx5.c5.kafka.ap-southeast-1.amazonaws.com:9096"
  KAFKA_USERNAME: "vhm-vinclub-stag-msk-consumer"
  KAFKA_PASSWORD: "vhm-vinclub-stag-msk-consumerabcd!"
  KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
  KAFKA_SASL_JAAS_CONFIG: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vhm-vinclub-stag-msk-consumer\" password=\"vhm-vinclub-stag-msk-consumerabcd!\";"
  KAFKA_SECURITY_PROTOCOL: "SASL_SSL"
  KAFKA_MAX_POLL_RECORDS: "1"

  VCLUB_CUSTOMER_JWT_PUBLIC_KEY_RS256: |
    -----BEGIN PUBLIC KEY-----
    MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAh4z4zEl1yHPne+QM3Iyy
    nbeSHf0HURwfgghhGoleJYC4XkAVFVvqHv+LmQu3UuM0JFdB4GllaGJJaAnSdU26
    aGIctYITlXytQZcEptooCguWGh0VgMdF+DH74q+ZO+yLDtv74irr+KsSHC4qs5gF
    PG000eStbEvd7A8nlbbdldsYOKMza51YIsnDNcYifjoRANBeVxn2pV3f810/mqZr
    tSls+/YndQ9SGCQrtIIDrQotnBBJyZdOSBPo7aWyMcFl+Sv7H1Ngm6MZyWwkqSo1
    kQ7iJAjJyPHZMmlQqLNdAqZT9vryvv4h8T4WvriYzNbdvXymtmF6sVU9dgi2zGhc
    eAGQuAbYKz1FUagbdBL6lKeIdzlnrWlKSL/TR+6Feg6dbL9c1GfMhTqq8MeAKLuK
    AuCBU220AkG1f0ZOD3pw7J6laWCHtz6brp2tdWQc4Q3UNuGWtNGygxP5GCDF9/7P
    i0cB04k5A2wO/1otU7tAeSTjMC/ZZYpmsrIC+CPyRsHNDwkjRDKY5gl1Hnd8KgiG
    IkwlHt7wsFAzmQzYsaOFXXs4LAX8QdxPLOvEtsl5nWOB1LSx5k0HQUzGgFMeqLiL
    59YjCqqJxQYLZsLnfvoHx0JVDcEYpx19ayhOZd3sXXjqNGOkkCzZy01dBfuOMixm
    fkbA/QvfH7wSbvhKCZJrflkCAwEAAQ==
    -----END PUBLIC KEY-----

  GALAXYPAY_BASE_URL: "https://uat-secure.galaxypay.vn/api/v1"
  GALAXYPAY_APIKEY: "ICAgeyIxMDE5OSI6IjEwMTk5IiwiY3JlYXRlZCI6MjAyNDEyMTMxNDM5MzB9.03a799a9d74f7522804ca4e95548064fc8795fc6750cb6d002c333c7e0ed8243"
  GALAXYPAY_SALT: "qkO2jxQntT"
  GALAXYPAY_CALLBACK_IPN_URL: "https://stag-api.vinclub.vn/ei/payment/paygate/galaxypay/callback/ipn"
  GALAXYPAY_CALLBACK_APP_URL: "vinclubd://app/app/payment/callback?txn_type=$txn_type&txn_id=$txn_id&status=$status"

  CORE_SERVICE_ENDPOINT: "http://vclub-core-service.vinclub-backend-stag:80/r"
  AUTHZ_VCLB_CORE_SVC_TOKEN: "Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.K6Bi0qxZYiTZF6Y9Dvqs89bZoUqJFkB7lrsCxBhFLhfOKypeEvcXbwe9ckRw6IHNuYJBV0Fj3MT9-Ryp9vewIGSwcwvaZ8DcvxDEb8U2UaxDnhpbSO4H4M0bNFspBwADiupsryJ5-riJC66nX7oIcI6_8yYbabd9i4ILDps5Vinu2bCqQ2bwjiiraZabeWQsfHA8S9iIMDKTrV86cvWOkAXSmG4Hohssz0pVE6QkzQ6P-s6w91ypWtizskfQqb5dtWOogHGdFhsLHTNhM_FCCcP8Pb8a8bWuBJa3GiVdoBmB9EVOQL5f-bleGvCmHfvUuvOl3SlnUjOen9ainmmxqg"

  TOPUP_POINT_LIMIT_POINT_DAILY_DEFAULT: "50000"
  TOPUP_POINT_LIMIT_POINT_MONTHLY_DEFAULT: "100000"
  TOPUP_POINT_LIMIT_TRANSACTION_DAILY_DEFAULT: "-1"
  TOPUP_POINT_ALLOWED_TIER_IDS: "1,2,3,4"
  TOPUP_POINT_WHITELIST_CUSTOMER_IDS: ""
  TOPUP_POINT_MIN_POINT_ALLOWED: "10"
  TOPUP_POINT_MAX_POINT_ALLOWED: "5000"

  # REST TEMPLATE PROXY
  PROXY_HOST: "*************"
  PROXY_PORT: "30080"

  # INVOICE CONFIG
  MAX_RECENT_INVOICE_BUYER_INFOS: "5"
  INVOICE_COMPANY_ALIAS: "VC"

  # VNPT INVOICE PROVIDER CONFIG
  VNPT_INVOICE_PROVIDER_URL: "http://tt78-vf.vingroup.local"
  VNPT_INVOICE_PROVIDER_ACCOUNT: "vfpub"
  VNPT_INVOICE_PROVIDER_AC_PASS: "123456aA@"
  VNPT_INVOICE_PROVIDER_USERNAME: "vfservice"
  VNPT_INVOICE_PROVIDER_PASSWORD: "123456aA@"
