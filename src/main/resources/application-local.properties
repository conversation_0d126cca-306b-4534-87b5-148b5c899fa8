#
spring.application.name=payment-service
server.port = 8080
server.servlet.context-path = /pm
spring.main.allow-bean-definition-overriding=true
#
#
logging.config=classpath:logback-spring.xml
spring.main.banner-mode=off

server.max-http-request-header-size=2MB
#
server.servlet.encoding.charset = UTF-8
server.servlet.session.tracking-modes = url

########## PostgreSQL - connection
spring.datasource.url = ${POSTGRESQL_URL:********************************************************************}
spring.datasource.username = ${POSTGRESQL_USER:vhm_user}
spring.datasource.password = ${POSTGRESQL_PASSWORD:vhm_pass}
spring.datasource.hikari.minimumIdle = 2
spring.datasource.hikari.maximumPoolSize = 3
spring.datasource.hikari.idleTimeout = 30000
spring.datasource.hikari.poolName = SpringBootJPAHikariCP
spring.datasource.hikari.maxLifetime = 2000000
spring.datasource.hikari.connectionTimeout = 30000

# The SQL dialect makes Hibernate generate better SQL for the chosen database
#spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
#drop n create table again, good for testing, comment this in production
# Hibernate ddl auto (create, create-drop, validate, update, none)
spring.jpa.properties.hibernate.default_schema = ${DB_DEFAULT_SCHEMA:payment_db}
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults = false
spring.jpa.database-platform = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto = update
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql = true
#logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

spring.jackson.property-naming-strategy = LOWER_CAMEL_CASE

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS:redis://localhost:6379}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:64}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:24}

#kafka configuration
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.plain.PlainLoginModule required username="vhm-user" password="vhm-pass";}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER:localhost:9092}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.max-poll-records = ${KAFKA_MAX_POLL_RECORDS:10}
kafka.properties.auto.offset.reset = ${KAFKA_AUTO_OFFSET_RESET:}

kafka.properties.main.concurrency = ${KAFKA_MAIN_CONCURRENCY:2}

kafka.message_notification.topic.name=${KAFKA_MESSAGE_NOTIFICATION_TOPIC_NAME:message_delivery}
kafka.properties.main.group.name=payment-service.consumer.main_${spring.profiles.active}

kafka.payment_history_status_changed.topic=payment_svc.payment_status_changed

kafka.issue_invoice.topic=payment_svc.issue_invoice
kafka.issue_invoice.group.name=payment_svc.consumer.issue_invoice_${spring.profiles.active}

kafka.historical.payment_history.topic.name=historical.payment_history
kafka.historical.payment_history.update_invoice_buyer_info_recent.group.name=payment_svc.consumer.historical.payment_history_to_update_invoice_buyer_info_${spring.profiles.active}
kafka.historical.payment_history.create_invoice.group.name=payment_svc.consumer.historical.payment_history_to_create_invoice_${spring.profiles.active}
#
vinclub.profiler.key = ${PROFILER_KEY:VinClub@123!}
springdoc.api-docs.path = /api-docs
springdoc.swagger-ui.path = /swagger-ui.html
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=error

vinclub.logging.level.client.RestTemplate = "DEBUG"

payment_gateway.galaxypay.api_v1_url = ${GALAXYPAY_BASE_URL:https://uat-secure.galaxypay.vn/api/v1}
payment_gateway.galaxypay.apikey = ${GALAXYPAY_APIKEY:ICB7IjEiOiIxIiwiY3JlYXRlZCI6MjAyMjA4MTcxNjE0NTZ9.c4088df3ca18648ea0078dda0b879b32ba646730c2bf41b0255cc26d006f3e17}
payment_gateway.galaxypay.salt = ${GALAXYPAY_SALT:YIDSN9ATEH}

payment_gateway.galaxypay.callback_ipn_url = ${GALAXYPAY_CALLBACK_IPN_URL:https://stag-api.vinclub.vn/ei/payment/paygate/galaxypay/callback/ipn}
payment_gateway.galaxypay.callback_app_url = ${GALAXYPAY_CALLBACK_APP_URL:https://stag.vinclub.vn/app/payment/callback?txn_type=$txn_type&txn_id=$txn_id&status=$status}

vclub_customer.jwt.public_key_rs256 = ${VCLUB_CUSTOMER_JWT_PUBLIC_KEY_RS256:MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAh4z4zEl1yHPne+QM3IyynbeSHf0HURwfgghhGoleJYC4XkAVFVvqHv+LmQu3UuM0JFdB4GllaGJJaAnSdU26aGIctYITlXytQZcEptooCguWGh0VgMdF+DH74q+ZO+yLDtv74irr+KsSHC4qs5gFPG000eStbEvd7A8nlbbdldsYOKMza51YIsnDNcYifjoRANBeVxn2pV3f810/mqZrtSls+/YndQ9SGCQrtIIDrQotnBBJyZdOSBPo7aWyMcFl+Sv7H1Ngm6MZyWwkqSo1kQ7iJAjJyPHZMmlQqLNdAqZT9vryvv4h8T4WvriYzNbdvXymtmF6sVU9dgi2zGhceAGQuAbYKz1FUagbdBL6lKeIdzlnrWlKSL/TR+6Feg6dbL9c1GfMhTqq8MeAKLuKAuCBU220AkG1f0ZOD3pw7J6laWCHtz6brp2tdWQc4Q3UNuGWtNGygxP5GCDF9/7Pi0cB04k5A2wO/1otU7tAeSTjMC/ZZYpmsrIC+CPyRsHNDwkjRDKY5gl1Hnd8KgiGIkwlHt7wsFAzmQzYsaOFXXs4LAX8QdxPLOvEtsl5nWOB1LSx5k0HQUzGgFMeqLiL59YjCqqJxQYLZsLnfvoHx0JVDcEYpx19ayhOZd3sXXjqNGOkkCzZy01dBfuOMixmfkbA/QvfH7wSbvhKCZJrflkCAwEAAQ==}

vclub_exchange_rate.unit.amount = ${EXCHANGE_MONEY_UNIT_AMOUNT:1000}
vclub_exchange_rate.point.per.unit.amount = ${POINT_PER_MONEY_UNIT_AMOUNT:1}

config.topup-point.limit.daily.default = ${TOPUP_POINT_LIMIT_POINT_DAILY_DEFAULT:50000}
config.topup-point.limit.monthly.default = ${TOPUP_POINT_LIMIT_POINT_MONTHLY_DEFAULT:100000}
config.topup-point.limit.transaction.daily.default=${TOPUP_POINT_LIMIT_TRANSACTION_DAILY_DEFAULT:-1}
config.topup-point.allowed-tier-ids=${TOPUP_POINT_ALLOWED_TIER_IDS:1,2,3,4}
config.topup-point.whitelist-customer-ids=${TOPUP_POINT_WHITELIST_CUSTOMER_IDS:}
config.topup-point.min-point-allowed=${TOPUP_POINT_MIN_POINT_ALLOWED:50}
config.topup-point.max-point-allowed=${TOPUP_POINT_MAX_POINT_ALLOWED:5000}

core.service.url=http://stag-svc.vinclub.internal:30082/r
core.service.auth=Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.K6Bi0qxZYiTZF6Y9Dvqs89bZoUqJFkB7lrsCxBhFLhfOKypeEvcXbwe9ckRw6IHNuYJBV0Fj3MT9-Ryp9vewIGSwcwvaZ8DcvxDEb8U2UaxDnhpbSO4H4M0bNFspBwADiupsryJ5-riJC66nX7oIcI6_8yYbabd9i4ILDps5Vinu2bCqQ2bwjiiraZabeWQsfHA8S9iIMDKTrV86cvWOkAXSmG4Hohssz0pVE6QkzQ6P-s6w91ypWtizskfQqb5dtWOogHGdFhsLHTNhM_FCCcP8Pb8a8bWuBJa3GiVdoBmB9EVOQL5f-bleGvCmHfvUuvOl3SlnUjOen9ainmmxqg


proxy.host=${PROXY_HOST:}
proxy.port=${PROXY_PORT:-1}

# Invoice configs
vinclub.invoice.max-recent-invoice-buyer-infos = ${MAX_RECENT_INVOICE_BUYER_INFOS:5}
vinclub.invoice.company-alias = ${INVOICE_COMPANY_ALIAS:VC}

# Invoice provider configs
invoice_provider.vnpt.url = ${VNPT_INVOICE_PROVIDER_URL:http://tt78-vf.vingroup.local}
invoice_provider.vnpt.account = ${VNPT_INVOICE_PROVIDER_ACCOUNT:vfpub}
invoice_provider.vnpt.ac_pass = ${VNPT_INVOICE_PROVIDER_AC_PASS:123456aA@}
invoice_provider.vnpt.username = ${VNPT_INVOICE_PROVIDER_USERNAME:vfservice}
invoice_provider.vnpt.password = ${VNPT_INVOICE_PROVIDER_PASSWORD:123456aA@}