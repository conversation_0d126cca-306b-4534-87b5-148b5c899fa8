#
spring.application.name=payment-service
server.port = 8080
server.servlet.context-path = /pm
spring.main.allow-bean-definition-overriding=true
#
#
logging.config=classpath:logback-prod.xml
spring.main.banner-mode=off

server.max-http-request-header-size=2MB
#
server.servlet.encoding.charset = UTF-8
server.servlet.session.tracking-modes = url

########## PostgreSQL - connection
spring.datasource.url = ${POSTGRESQL_URL}
spring.datasource.username = ${POSTGRESQL_USER}
spring.datasource.password = ${POSTGRESQL_PASSWORD}
spring.datasource.hikari.minimumIdle = 8
spring.datasource.hikari.maximumPoolSize = 24
spring.datasource.hikari.idleTimeout = 30000
spring.datasource.hikari.poolName = SpringBootJPAHikariCP
spring.datasource.hikari.maxLifetime = 2000000
spring.datasource.hikari.connectionTimeout = 30000

# The SQL dialect makes Hibernate generate better SQL for the chosen database
#spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
#drop n create table again, good for testing, comment this in production
# Hibernate ddl auto (create, create-drop, validate, update, none)
spring.jpa.properties.hibernate.default_schema = ${DB_DEFAULT_SCHEMA:payment_db}
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults = false
spring.jpa.database-platform = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto = none
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql = false
spring.jackson.property-naming-strategy = LOWER_CAMEL_CASE

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS}
redisson.config.password=${REDISSON_CONFIG_PASSWORD}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:64}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:24}

#kafka configuration
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.max-poll-records = ${KAFKA_MAX_POLL_RECORDS:10}
kafka.properties.auto.offset.reset = ${KAFKA_AUTO_OFFSET_RESET:}

kafka.properties.main.concurrency = ${KAFKA_MAIN_CONCURRENCY:2}

kafka.message_notification.topic.name=${KAFKA_MESSAGE_NOTIFICATION_TOPIC_NAME:message_delivery}
kafka.properties.main.group.name=payment-service.consumer.main_${spring.profiles.active}

kafka.payment_history_status_changed.topic=payment_svc.payment_status_changed

kafka.issue_invoice.topic=payment_svc.issue_invoice
kafka.issue_invoice.group.name=payment_svc.consumer.issue_invoice_${spring.profiles.active}

kafka.historical.payment_history.topic.name=historical.payment_history
kafka.historical.payment_history.update_invoice_buyer_info_recent.group.name=payment_svc.consumer.historical.payment_history_to_update_invoice_buyer_info_${spring.profiles.active}
kafka.historical.payment_history.create_invoice.group.name=payment_svc.consumer.historical.payment_history_to_create_invoice_${spring.profiles.active}
#
vinclub.profiler.key = ${PROFILER_KEY:VinClub@123!}

vinclub.logging.level.client.RestTemplate = "DEBUG"

payment_gateway.galaxypay.api_v1_url = ${GALAXYPAY_BASE_URL}
payment_gateway.galaxypay.apikey = ${GALAXYPAY_APIKEY}
payment_gateway.galaxypay.salt = ${GALAXYPAY_SALT}

payment_gateway.galaxypay.callback_ipn_url = ${GALAXYPAY_CALLBACK_IPN_URL}
payment_gateway.galaxypay.callback_app_url = ${GALAXYPAY_CALLBACK_APP_URL}

vclub_customer.jwt.public_key_rs256 = ${VCLUB_CUSTOMER_JWT_PUBLIC_KEY_RS256}

vclub_exchange_rate.unit.amount = ${EXCHANGE_MONEY_UNIT_AMOUNT}
vclub_exchange_rate.point.per.unit.amount = ${POINT_PER_MONEY_UNIT_AMOUNT}

config.topup-point.limit.daily.default = ${TOPUP_POINT_LIMIT_POINT_DAILY_DEFAULT}
config.topup-point.limit.monthly.default = ${TOPUP_POINT_LIMIT_POINT_MONTHLY_DEFAULT}
config.topup-point.limit.transaction.daily.default=${TOPUP_POINT_LIMIT_TRANSACTION_DAILY_DEFAULT:-1}
config.topup-point.allowed-tier-ids=${TOPUP_POINT_ALLOWED_TIER_IDS}
config.topup-point.whitelist-customer-ids=${TOPUP_POINT_WHITELIST_CUSTOMER_IDS:}
config.topup-point.min-point-allowed=${TOPUP_POINT_MIN_POINT_ALLOWED}
config.topup-point.max-point-allowed=${TOPUP_POINT_MAX_POINT_ALLOWED}

core.service.url=${CORE_SERVICE_ENDPOINT}
core.service.auth=${AUTHZ_VCLB_CORE_SVC_TOKEN}

proxy.host=${PROXY_HOST}
proxy.port=${PROXY_PORT}

# Invoice configs
vinclub.invoice.max-recent-invoice-buyer-infos = ${MAX_RECENT_INVOICE_BUYER_INFOS:5}
vinclub.invoice.company-alias = ${INVOICE_COMPANY_ALIAS:VC}

# Invoice provider configs
invoice_provider.vnpt.url = ${VNPT_INVOICE_PROVIDER_URL}
invoice_provider.vnpt.account = ${VNPT_INVOICE_PROVIDER_ACCOUNT}
invoice_provider.vnpt.ac_pass = ${VNPT_INVOICE_PROVIDER_AC_PASS}
invoice_provider.vnpt.username = ${VNPT_INVOICE_PROVIDER_USERNAME}
invoice_provider.vnpt.password = ${VNPT_INVOICE_PROVIDER_PASSWORD}