spring.application.name = vclub-payment-service
spring.profiles.active = ${ENV:local}
spring.jackson.property-naming-strategy = LOWER_CAMEL_CASE
logging.config = classpath:logback-spring.xml

# Enable Hibernate SQL slow query logging
logging.level.org.hibernate.SQL_SLOW=DEBUG

point.product.name = ${POINT_PRODUCT_NAME:?i?m VPoint}
point.product.unit.name = ${POINT_PRODUCT_UNIT_NAME:?i?m}
point.vat.rate = ${POINT_VAT_RATE:0.1}