spring.application.name = vclub-payment-service
spring.profiles.active = ${ENV:local}
spring.jackson.property-naming-strategy = LOWER_CAMEL_CASE
# Jackson numeric serialization configuration
spring.jackson.generator.write-numbers-as-strings = false
spring.jackson.serialization.write-numbers-as-strings = false
spring.jackson.deserialization.use-big-decimal-for-floats = false
spring.jackson.deserialization.use-big-integer-for-ints = false
logging.config = classpath:logback-spring.xml

# Enable Hibernate SQL slow query logging
logging.level.org.hibernate.SQL_SLOW=DEBUG

point.product.name = ${POINT_PRODUCT_NAME:?i?m VPoint}
point.product.unit.name = ${POINT_PRODUCT_UNIT_NAME:?i?m}
point.vat.rate = ${POINT_VAT_RATE:0.1}