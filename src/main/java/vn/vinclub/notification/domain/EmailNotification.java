package vn.vinclub.notification.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import org.springframework.lang.Nullable;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 11/27/24 15:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EmailNotification {

    private String from;
    private EmailTo to;
    private EmailMessage msg;

    @Nullable
    private List<EmailAddress> replyTo;

    @Nullable
    private String trackingId;

    @Nullable
    private String fromName;

    public String getNotificationType() {
        return "EMAIL_V2";
    }

}
