package vn.vinclub.payment.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;

@FieldNameConstants
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@NoArgsConstructor
public abstract class Auditable implements Serializable {

    private static final long serialVersionUID = 1L;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @LastModifiedBy
    @Column(name = "updated_by")
    private String updatedBy;

    @CreatedDate
    @Column(name = "created_on", updatable = false, columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime createdOn;

    @LastModifiedDate
    @Column(name = "updated_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime updatedOn;

    @JsonProperty("created_time")
    public Long getCreatedTime() {
        return createdOn != null ? createdOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    @JsonProperty("updated_time")
    public Long getUpdatedTime() {
        return updatedOn != null ? updatedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

}
