package vn.vinclub.payment.model;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;

import java.util.List;

@Entity
@Table(name = "customer_invoice_buyer_info")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
/**
 * Thông tin hóa đơn người mua gần đây của khách hàng.
 *
 */
public class CustomerInvoiceBuyerInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	@Column(name = "customer_id")
	private Long customerId;

	@Type(JsonBinaryType.class)
	@Column(name = "recent_invoice_buyer_infos", columnDefinition = "jsonb")
	private List<InvoiceBuyerInfo> recentInvoiceBuyerInfos;

}
