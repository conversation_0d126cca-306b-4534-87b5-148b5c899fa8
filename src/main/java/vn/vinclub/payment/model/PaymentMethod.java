package vn.vinclub.payment.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import vn.vinclub.payment.converter.PaymentGatewayEnumConverter;
import vn.vinclub.payment.converter.PaymentMethodEnumConverter;
import vn.vinclub.payment.converter.PaymentMethodStatusEnumConverter;
import vn.vinclub.payment.converter.PaymentServiceProviderEnumConverter;
import vn.vinclub.payment.dto.FeeConfig;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentMethodStatusEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 12/11/24 14:23
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Builder
@Table(name = "payment_method")
public class PaymentMethod extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "active", columnDefinition = "BOOLEAN NOT NULL DEFAULT true")
    private Boolean active;

    @Column(name = "deleted_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime deletedOn;

    @JsonProperty("updated_time")
    public Long getDeletedTime() {
        return deletedOn != null
                ? deletedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                : null;
    }

    @Column(name = "status")
    @Convert(converter = PaymentMethodStatusEnumConverter.class)
    private PaymentMethodStatusEnum status;

    @Column(name = "payment_method_type")
    @Convert(converter = PaymentMethodEnumConverter.class)
    private PaymentMethodEnum paymentMethodType;

    @Column(name = "service_provider_type")
    @Convert(converter = PaymentServiceProviderEnumConverter.class)
    private PaymentServiceProviderEnum serviceProviderType;

    @Column(name = "service_provider_id")
    private Long serviceProviderId;

    @Column(name = "pgw_type")
    @Convert(converter = PaymentGatewayEnumConverter.class)
    private PaymentGatewayEnum pgwType;

    @Column(name = "pgw_id")
    private Long pgwId;

    @Column(name = "display_order")
    private Integer displayOrder;

    @Type(JsonBinaryType.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, String> displayNames;

    @Column(name = "fee_required")
    private Boolean feeRequired;

    @Column(name = "fee_percentage")
    private BigDecimal feePercentage;

    @Column(name = "fixed_fee")
    private BigDecimal fixedFee;

    @Type(JsonBinaryType.class)
    @Column(name = "fee_configs", columnDefinition = "jsonb")
    private List<FeeConfig> feeConfigs;

    @Column(name = "min_amount_allowed")
    private BigDecimal minAmountAllowed;

    @Column(name = "max_amount_allowed")
    private BigDecimal maxAmountAllowed;

}
