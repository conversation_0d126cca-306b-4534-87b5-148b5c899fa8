package vn.vinclub.payment.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import vn.vinclub.payment.converter.CurrencyEnumConverter;
import vn.vinclub.payment.converter.PaymentGatewayEnumConverter;
import vn.vinclub.payment.converter.PaymentMethodEnumConverter;
import vn.vinclub.payment.converter.PaymentServiceProviderEnumConverter;
import vn.vinclub.payment.converter.PaymentStatusEnumConverter;
import vn.vinclub.payment.converter.TransactionTypeEnumConverter;
import vn.vinclub.payment.dto.FeeDetail;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR> 12/11/24 14:23
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Builder
@Table(name = "payment_history")
public class PaymentHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "active", columnDefinition = "BOOLEAN NOT NULL DEFAULT true")
    private boolean active;

    @Column(name = "deleted_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime deletedOn;

    @JsonProperty("deleted_time")
    public Long getDeletedTime() {
        return deletedOn != null
                ? deletedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                : null;
    }

    @Column(name = "payment_id")
    private String paymentId;

    @Column(name = "original_payment_id")
    private String originalPaymentId;

    @Column(name = "payment_date", updatable = false, columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime paymentDate;

    @JsonProperty("payment_time")
    public Long getPaymentTime() {
        return paymentDate != null ? paymentDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    @Column(name = "status")
    @Convert(converter = PaymentStatusEnumConverter.class)
    private PaymentStatusEnum status;

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "transaction_type")
    @Convert(converter = TransactionTypeEnumConverter.class)
    private TransactionTypeEnum transactionType;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "fee_amount")
    private BigDecimal feeAmount;

    @Type(JsonBinaryType.class)
    @Column(name = "fee_details", columnDefinition = "jsonb")
    private List<FeeDetail> feeDetails;

    @Column(name = "discount_amount")
    private BigDecimal discountAmount;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "currency")
    @Convert(converter = CurrencyEnumConverter.class)
    private CurrencyEnum currency; // CurrencyEnum

    @Column(name = "description")
    private String description;

    @Column(name = "payment_method_type")
    @Convert(converter = PaymentMethodEnumConverter.class)
    private PaymentMethodEnum paymentMethodType;

    @Column(name = "payment_method_id")
    private Long paymentMethodId;

    @Column(name = "service_provider_type")
    @Convert(converter = PaymentServiceProviderEnumConverter.class)
    private PaymentServiceProviderEnum serviceProviderType;

    @Column(name = "service_provider_id")
    private Long serviceProviderId;

    @Column(name = "pgw_type")
    @Convert(converter = PaymentGatewayEnumConverter.class)
    private PaymentGatewayEnum pgwType;

    @Column(name = "pgw_id")
    private Long pgwId;

    @Column(name = "pgw_txn_id")
    private String pgwTxnId;

    @Type(JsonBinaryType.class)
    @Column(name = "data_request", columnDefinition = "jsonb")
    private ObjectNode dataRequest;

    @Type(JsonBinaryType.class)
    @Column(name = "pgw_txn_response", columnDefinition = "jsonb")
    private ObjectNode pgwTxnResponse;

    @Column(name = "pgw_txn_response_code")
    private String pgwTxnResponseCode;

    @Column(name = "is_retry")
    private boolean retry;

    @Type(JsonBinaryType.class)
    @Column(name = "invoice_buyer_info", columnDefinition = "jsonb")
    private InvoiceBuyerInfo invoiceBuyerInfo;

}
