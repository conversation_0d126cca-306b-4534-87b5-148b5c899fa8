package vn.vinclub.payment.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.vinclub.payment.converter.*;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@FieldNameConstants
@Entity
@Table(name = "invoices")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Invoice extends BaseEntity {

    @Column(name = "transaction_type")
    @Convert(converter = InvoiceTransactionTypeEnumConverter.class)
    private InvoiceTransactionTypeEnum transactionType;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "source")
    @Convert(converter = InvoiceSourceEnumConverter.class)
    private InvoiceSourceEnum source;

    @Column(name = "brand_id")
    private String brandId;

    @Column(name = "pattern_no")
    private String patternNo;

    @Column(name = "serial_no")
    private String serialNo;

    @Column(name = "invoice_no")
    private Long invoiceNo;

    @Column(name = "issued_at", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime issuedAt;

    @Column(name = "status")
    @Convert(converter = InvoiceIssueStatusEnumConverter.class)
    private InvoiceIssueStatusEnum status;

    @Column(name = "issue_type")
    @Convert(converter = InvoiceIssueTypeEnumConverter.class)
    private InvoiceIssueTypeEnum issueType;

    @Column(name = "active", columnDefinition = "BOOLEAN NOT NULL DEFAULT true")
    private boolean active;

    @Type(JsonBinaryType.class)
    @Column(name = "buyer_info", columnDefinition = "jsonb")
    private InvoiceBuyerInfo buyerInfo;

    @Type(JsonBinaryType.class)
    @Column(name = "items", columnDefinition = "jsonb")
    private List<InvoiceProduct> items;

    @Column(name = "currency")
    @Convert(converter = CurrencyEnumConverter.class)
    private CurrencyEnum currency;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "vat_amount")
    private BigDecimal vatAmount;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    @Column(name = "provider")
    @Convert(converter = InvoiceProviderEnumConverter.class)
    private InvoiceProviderEnum provider;

    @Column(name = "provider_unique_id")
    private String providerUniqueId;

    @Column(name = "provider_request")
    private String providerRequest;

    @Column(name = "provider_response")
    private String providerResponse;

    @Column(name = "sap_id")
    private String sapId;

    @Column(name = "sap_posting_status")
    @Convert(converter = SapPostingStatusEnumConverter.class)
    private SapPostingStatusEnum sapPostingStatus;

    @Column(name = "sap_posting_at", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime sapPostingAt;

    @Column(name = "sap_request")
    private String sapRequest;

    @Column(name = "sap_response")
    private String sapResponse;

    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private JsonNode metadata;

    public void putMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = JsonNodeFactory.instance.objectNode();
        }
        switch (value) {
            case String s -> ((ObjectNode) metadata).put(key, s);
            case Number n -> ((ObjectNode) metadata).put(key, n.longValue());
            case Boolean b -> ((ObjectNode) metadata).put(key, b);
            case JsonNode jn -> ((ObjectNode) metadata).set(key, jn);
            case null -> ((ObjectNode) metadata).remove(key);
            default -> ((ObjectNode) metadata).putPOJO(key, value);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvoiceProduct {
        private String productName;
        private String productUnit;
        private Long quantity;
        private BigDecimal unitPrice;
        private BigDecimal amount; // unitPrice * quantity

        private Integer vatRate; // -2=không tính thuế; -1=không chịu thuế; 0=0%; 5=5%; 8=8%; 10=10%
        private BigDecimal vatAmount; // amount * vatRate / 100

        private BigDecimal totalAmount;

        private String note;
    }
}

