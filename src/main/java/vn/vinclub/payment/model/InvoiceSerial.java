package vn.vinclub.payment.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import vn.vinclub.payment.converter.InvoiceProviderEnumConverter;
import vn.vinclub.payment.converter.InvoiceSourceEnumConverter;
import vn.vinclub.payment.enums.InvoiceProviderEnum;
import vn.vinclub.payment.enums.InvoiceSourceEnum;

@FieldNameConstants
@Entity
@Table(name = "invoice_serials")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceSerial extends BaseEntity {

    @Column(name = "provider")
    @Convert(converter = InvoiceProviderEnumConverter.class)
    private InvoiceProviderEnum provider;

    @Column(name = "brand_id")
    private String brandId;

    @Column(name = "pattern_no")
    private String patternNo;

    @Column(name = "serial_no")
    private String serialNo;

    @Column(name = "start_invoice_no")
    private Long startInvoiceNo;

    @Column(name = "current_invoice_no")
    private Long currentInvoiceNo;

    @Column(name = "end_invoice_no")
    private Long endInvoiceNo;

    @Column(name = "used_for")
    @Convert(converter = InvoiceSourceEnumConverter.class)
    private InvoiceSourceEnum usedFor;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "active", columnDefinition = "BOOLEAN NOT NULL DEFAULT true")
    private boolean active;

}
