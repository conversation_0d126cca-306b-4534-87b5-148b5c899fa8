package vn.vinclub.payment.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Type;
import vn.vinclub.payment.enums.OutboxStatusEnum;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "outbox_event")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class OutboxEvent extends BaseEntity {

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "event_code")
    private String eventCode;

    @Column(name = "message_key")
    private String messageKey;

    @Type(JsonBinaryType.class)
    @Column(name = "payload", columnDefinition = "jsonb")
    private JsonNode payload;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private OutboxStatusEnum status;

    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private JsonNode metadata;
}
