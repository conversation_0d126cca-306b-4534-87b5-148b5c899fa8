package vn.vinclub.payment.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.Convert;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.vinclub.payment.converter.PaymentGatewayEnumConverter;
import vn.vinclub.payment.converter.PaymentMethodEnumConverter;
import vn.vinclub.payment.dto.SignedUrlInfoDto;
import vn.vinclub.payment.enums.PaymentMethodEnum;

/**
 * <AUTHOR> 12/11/24 14:23
 */
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Builder
@Table(name = "payment_service_provider")
public class PaymentServiceProvider extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "active", columnDefinition = "BOOLEAN NOT NULL DEFAULT true")
    private Boolean active;

    @Column(name = "deleted_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime deletedOn;

    @JsonProperty("updated_time")
    public Long getDeletedTime() {
        return deletedOn != null ? deletedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    @Column(name = "payment_method")
    @Convert(converter = PaymentMethodEnumConverter.class)
    private PaymentMethodEnum paymentMethod;

    @Column(name = "payment_method_provider")
    private String paymentMethodProvider;

    @Column(name = "name")
    private String name;

    @Type(JsonBinaryType.class)
    @Column(name = "display_names", columnDefinition = "jsonb")
    private Map<String, String> displayNames;

    @Type(JsonBinaryType.class)
    @Column(name = "logo", columnDefinition = "jsonb")
    private Map<String, SignedUrlInfoDto> logo;

    @Column(name = "display_order")
    private Integer displayOrder;

}
