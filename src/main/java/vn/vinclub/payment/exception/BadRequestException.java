package vn.vinclub.payment.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import vn.vinclub.payment.util.ServiceResponse;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BadRequestException extends BusinessLogicException {
	private static final long serialVersionUID = 1L;

	public BadRequestException(ServiceResponse<?> payload) {
		super(payload);
	}

}
