package vn.vinclub.payment.exception.handler;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.util.ServiceResponse;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class ExceptionResponse extends ServiceResponse {
    private static final long serialVersionUID = 1L;
    private Instant timestamp;

    public ExceptionResponse(int code, List<String> message) {
        setCode(code);
        setMessage(message);
        this.timestamp = Instant.now();
    }

    public ExceptionResponse(int code, String message) {
        setCode(code);
        setMessage(Arrays.asList(message));
        this.timestamp = Instant.now();
    }

}
