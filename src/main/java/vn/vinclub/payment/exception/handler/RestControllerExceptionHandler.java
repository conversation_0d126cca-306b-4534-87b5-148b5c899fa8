package vn.vinclub.payment.exception.handler;


import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.exception.ResourceNotFoundException;
import vn.vinclub.payment.util.ServiceResponse;

import java.io.IOException;
import java.nio.file.AccessDeniedException;
import java.util.List;
import java.util.stream.Collectors;

@ControllerAdvice
@Slf4j
public class RestControllerExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseBody
    public ServiceResponse<String> resolveException(ResourceNotFoundException exception) {
        return ServiceResponse.error(AppErrorCode.NOT_FOUND.getCode(), exception.getMessage());
    }

    @ExceptionHandler({BusinessLogicException.class, BadRequestException.class})
    public ResponseEntity<?> handleBusinessException(BusinessLogicException e) {
        return new ResponseEntity<>(e.getPayload(), HttpStatus.BAD_REQUEST);
    }

    @Override
    public ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {

//        log.error("handleMethodArgumentNotValid: {}\theader={}\tstatus={}\tWebRequest={}", ex.getMessage(), headers, status, request, ex);

        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        return new ResponseEntity<>(new ExceptionResponse(AppErrorCode.BAD_REQUEST.getCode(), fieldErrors.stream().map(e -> {
            if (null == e.getDefaultMessage())
                return e.getDefaultMessage();

            switch (e.getDefaultMessage()) {
                case "must not be null":
                case "must not be empty":
                case "must not be blank":
                    return String.format("%s: %s", e.getField(), e.getDefaultMessage());
                default:
                    return e.getDefaultMessage();
            }
        }).collect(Collectors.toList()))
                , HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleHttpMessageNotReadable: {}\theader={}\tstatus={}\tWebRequest={}", ex.getMessage(), headers, status, request, ex);

        return new ResponseEntity<>(
                new ExceptionResponse(AppErrorCode.INVALID_PARAM.getCode(), AppErrorCode.VALUE_INVALID.getMessage()),
                HttpStatus.BAD_REQUEST
        );
    }


    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String message = String.format(AppErrorCode.METHOD_NOT_ALLOWED.getMessage(), ex.getMethod(), ex.getSupportedHttpMethods());

        log.error("handleHttpRequestMethodNotSupported: {}\theader={}\tstatus={}\tWebRequest={}", message, headers, status, request, ex);

        return new ResponseEntity<>(
                new ExceptionResponse(AppErrorCode.METHOD_NOT_ALLOWED.getCode(), message),
                HttpStatus.METHOD_NOT_ALLOWED
        );
    }

    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ExceptionResponse> handleServerException(RuntimeException ex) {
        log.error("handleServerException: {}", ex.getMessage(), ex);
        return new ResponseEntity<>(
                new ExceptionResponse(AppErrorCode.INTERNAL_SERVER_ERROR.getCode(), AppErrorCode.INTERNAL_SERVER_ERROR.getMessage()),
                HttpStatus.BAD_REQUEST
        );
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleExceptionInternal: {}\theader={}\tbody={}\tWebRequest={}", ex.getMessage(), headers, body, request);
//		if (StringUtils.hasText(ex.getMessage())) {
//			rabbitSender.sendToErrorLog(ex.getMessage());
//		}
        return new ResponseEntity<>(
                new ExceptionResponse(AppErrorCode.INTERNAL_SERVER_ERROR.getCode(), ex.getMessage()),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler(value = AccessDeniedException.class)
    public void handleConflict(HttpServletResponse response) throws IOException {
        response.sendError(HttpStatus.FORBIDDEN.value(), "Access Denied");
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        return new ResponseEntity<>(new ExceptionResponse(AppErrorCode.BAD_REQUEST.getCode(), ex.getLocalizedMessage()), HttpStatus.BAD_REQUEST);
    }
}
