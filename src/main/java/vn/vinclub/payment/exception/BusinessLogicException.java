package vn.vinclub.payment.exception;

import lombok.Getter;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.util.ServiceResponse;

@Getter
public class BusinessLogicException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        super(JsonUtils.toString(payload));
        this.payload = payload;

    }
}
