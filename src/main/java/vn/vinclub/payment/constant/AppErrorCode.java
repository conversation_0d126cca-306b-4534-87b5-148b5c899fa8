package vn.vinclub.payment.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum AppErrorCode {
    //--
    SUCCESS(0, "Thành công"),
    ERROR(-1, "<PERSON>ó lỗi xảy ra"),

    // core
    CUSTOMER_NOT_FOUND(4019236, "Khách hàng không tồn tại"),

    //--
    JSON_INVALID(7020500, "Sai định dạng JSON"),
    VALUE_INVALID(7020501, "Sai định dạng dữ liệu"),
    BAD_REQUEST(7020502, ""),
    NOT_FOUND(7020503, "%s không tồn tại với %s = %s"),
    METHOD_NOT_ALLOWED(7020504, "Phương thức %s không hỗ trợ. <PERSON>h sách các phương thức hỗ trợ là: %s"),
    INTERNAL_SERVER_ERROR(7020505, "<PERSON><PERSON>t tiếc đã có lỗi xảy ra"),
    RATE_LIMIT_EXCEED(7020506, "<PERSON><PERSON><PERSON>i dùng gọi quá số lần sử dụng tối đa cho phép. Vui lòng thử lại sau"),

    SYSTEM_ERROR(7020600, "Lỗi hệ thống"),
    INVALID_PARAM(7020601, "Tham số %s không hợp lệ"),
    DATA_IN_USE(7020602, "Dữ liệu đang được sử dụng, vui lòng thử lại"),
    PARAM_REQUIRED(7020603, "Tham số %s là bắt buộc"),
    UPDATE_IN_PROGRESS(7020604, "Đang có một tiến trình thưc hiện cập nhật, vui lòng thử lại sau"),
    UPDATE_COMPLETED(7020605, "Tiến trình đã hoàn thành, vui lòng thử lại sau 10 phút"),
    DATE_RANGE_EXCEED_LIMIT(7020606, "Khoảng thời gian không được lớn hơn 90 ngày"),
    USER_INVALID(7020607, "Người dùng không hợp lệ"),

    REFRESH_TOKEN_INVALID(7020608, "Refresh token không hợp lệ"),
    USER_UNAUTHORIZED(7020609, "Người dùng không hợp lệ"),
    USER_CREDENTIAL_INVALID(7020610, "Thông tin đăng nhập không hợp lệ"),

    PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR(7020700, "PGW: Có lỗi xảy ra"),
    PAYMENT_GATEWAY_BAD_REQUEST(7020701, "PGW: Dữ liệu gửi sang gateway không hợp lệ"),

    PAYMENT_METHOD_NOT_EXIST(7020702, "Phương thức thanh toán không tồn tại"),
    PAYMENT_HISTORY_NOT_FOUND(7020703, "Thông tin thanh toán không tồn tại"),
    PAYMENT_EXCHANGE_RATE_NOT_EXIST(7020704, "Thông tin quy đổi không tồn tại"),

    MONEY_AMOUNT_NOT_MATCH(7020705, "Số tiền cần thanh toán không khớp với số tiền quy đổi"),
    MONEY_AMOUNT_NOT_ALLOWED(7020706, "Số tiền cần thanh toán không nằm trong khoảng hợp lệ (từ %s đến %s)"),

    PAYMENT_HISTORY_ID_NOT_MATCH(7020707, "Mã thanh toán không khớp"),
    PAYMENT_HISTORY_NO_RETRYABLE(7020708, "Không thể thực hiện thanh toán lại với giao dịch này"),
    CHECKOUT_PAYMENT_DUPLICATED(7020709, "Giao dịch trùng lặp với đang xử lý, vui lòng thử lại sau một lát"),

    RATE_LIMIT_EXCEED_LIMIT(7020710, "Thao tác quá nhanh, vui lòng thử lại sau"),
    PAYMENT_METHOD_NOT_ACTIVE(7020711, "Phương thức thanh toán không khả dụng"),

    TOPUP_LIMIT_EXCEEDED_DAILY(7020712, "Vượt quá hạn mức nạp điểm trong ngày"),
    TOPUP_LIMIT_EXCEEDED_MONTHLY(7020713, "Vượt quá hạn mức nạp điểm trong tháng"),

    POINT_AMOUNT_NOT_ALLOWED(7020714, "Số point không nằm trong khoảng hợp lệ (từ %s đến %s)"),
    TOPUP_NOT_ELIGIBILITY(7020715, "Khách hàng không đủ điều kiện để nạp VPoint"),

    TOPUP_POINT_QUOTA_EXCEEDED_TRANSACTION_DAILY_ALL(7020716, "Tính năng này đang giới hạn, bạn vui lòng thử lại vào ngày mai"),


    INVOICE_ALREADY_EXITS(7020800, "Hóa đơn đã tồn tại với mã giao dịch %s"),
    INVOICE_INVALID_STATUS_FOR_ISSUE(7020801, "Trạng thái hóa đơn không hợp lệ để phát hành"),
    INVOICE_INVALID_STATUS_FOR_DOWNLOAD(7020802, "Trạng thái hóa đơn không hợp lệ để tải về"),
    INVOICE_PROVIDER_ERROR(7020803, "Lỗi từ nhà cung cấp hóa đơn: %s"),
    INVOICE_PROVIDER_CHECK_ERROR(7020804, "Lỗi khi kiểm tra trạng thái hóa đơn: %s"),
    INVOICE_PROVIDER_SERIAL_NOT_FOUND(7020805, "Không tìm thấy dải hóa đơn"),
    INVOICE_PROVIDER_SERIAL_NOT_ENOUGH(7020806, "Dải hóa đơn không đủ số lượng"),
    INVOICE_PROVIDER_CERTIFICATE_EXPIRED(7020807, "Chứng thư hết hạn"),
    INVOICE_PROVIDER_DATE_INVALID(7020808, "Danh sách hóa đơn tồn tại ngày hóa đơn nhỏ hơn ngày hóa đơn đã phát hành"),
    INVOICE_PROVIDER_NO_INVALID(7020809, "Số hóa đơn truyền vào không hợp lệ, số hóa đơn hợp lệ phải là %s"),
    INVOICE_PROVIDER_INVALID_RESPONSE(7020810, "Dữ liệu trả về từ nhà cung cấp không hợp lệ"),
    INVOICE_PROVIDER_INVALID_REQUEST(7020811, "Dữ liệu gửi sang nhà cung cấp không hợp lệ"),
    INVOICE_PROVIDER_INVALID_CONFIG(7020812, "Cấu hình nhà cung cấp không hợp lệ: %s"),
    INVOICE_PROVIDER_DUPLICATE_INVOICE_NO(7020813, "Số hóa đơn đã tồn tại"),
    INVOICE_PROVIDER_DUPLICATE_UNIQUE_ID(7020814, "Mã hóa đơn đã tồn tại"),
    ;

    private final Integer code;
    private final String message;

    AppErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    // lookup table to be used to find enum for conversion
    private static final Map<Integer, vn.vinclub.payment.constant.AppErrorCode> lookup = new HashMap<>();

    static {
        for (vn.vinclub.payment.constant.AppErrorCode e : vn.vinclub.payment.constant.AppErrorCode.values())
            lookup.put(e.getCode(), e);
    }

    public static vn.vinclub.payment.constant.AppErrorCode lookupByErrorCode(Integer errorCode) {
        return lookup.get(errorCode);
    }

}
