package vn.vinclub.payment.constant;

import org.redisson.client.codec.Codec;
import org.redisson.client.codec.LongCodec;
import org.redisson.codec.SerializationCodec;

public interface AppConst {

    String VCLUB_USERID = "VCLUB_USER_ID";
    String VCLUB_CUSTOMER = "VCLUB_CUSTOMER";

    String CHECKOUT_URL = "checkoutUrl";
    String CRON_JOB_PAYMENT_SYNC = "cronJobPaymentSync";
    String IPN = "ipn";

    interface REDIS_LOCK {
        String TRANSACTION_HISTORY_CUD = "payment_svc:LOCK_TRANSACTION_HISTORY_CUD";
        String IDEMPOTENT_CHECKOUT_PAYMENT = "payment_svc:IDEMPOTENT_CHECKOUT_PAYMENT";
        String INVOICE_CUD = "payment_svc:LOCK_INVOICE_CUD:";
        String INVOICE_ISSUING = "payment_svc:INVOICE_ISSUING:";
        String RATE_LIMIT = "payment_svc:RATE_LIMIT";
    }

    interface REDIS_KEY {
        String POINT_TOPUP_BY_CUSTOMER_ID = "payment_svc:POINT_TOPUP_BY_CUSTOMER_ID:%s:%s";
        String QUOTA_PREFIX = "payment_svc:QUOTA:%s";
    }

    interface POPUP_POINT_LIMIT_TYPE {
        String DAILY = "DAILY";
        String MONTLY = "MONTLY";

    }
    String UPDATED_BY_AUTO = "SYSTEM";
    String SUCCESS = "SUCCESS";
    String ERROR = "ERROR";
    String DEFAULT_LANGUAGE = "vi";

    Codec DEFAULT_CODEC = new SerializationCodec();
    Codec LONG_CODEC = new LongCodec();
}
