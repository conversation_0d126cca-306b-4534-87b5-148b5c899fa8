package vn.vinclub.payment.kafka;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.dto.event.historical.HistoricalEvent;
import vn.vinclub.payment.dto.event.outbox.IssueInvoiceEvent;
import vn.vinclub.payment.dto.invoice.InvoiceRequestEvent;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.service.PaymentService;

import java.time.Duration;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaConsumer {


    private final InvoiceService invoiceService;
    private final PaymentService paymentService;

    @KafkaListener(
            topics = "${kafka.historical.payment_history.topic.name}",
            groupId = "${kafka.historical.payment_history.update_invoice_buyer_info_recent.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory"
    )
    public void consumePaymentHistoryHistoricalToUpdateInvoiceBuyerInfoRecent(ConsumerRecord<String, String> record, Acknowledgment ack) throws InterruptedException {
        log.info("[consumePaymentHistoryHistoricalToUpdateInvoiceBuyerInfoRecent][Kafka] Consumed message - topic={}, partition={}, offset={}, message={}",
                record.topic(), record.partition(), record.offset(), record.value());
        HistoricalEvent<PaymentHistory> event = null;
        try (Profiler p = new Profiler(getClass(), "consumePaymentHistoryHistoricalToUpdateInvoiceBuyerInfoRecent")) {
            String message = record.value();
            log.debug("[consumePaymentHistoryHistoricalToUpdateInvoiceBuyerInfoRecent] Start process message {}", message);

            event = JsonUtils.toObjectOrThrow(message, new TypeReference<>() {
            });

            if (event.getOldData() == null) {
                invoiceService.handleInvoiceRequestEvent(InvoiceRequestEvent.builder()
                        .customerId(event.getData().getCustomerId())
                        .invoiceBuyerInfo(event.getData().getInvoiceBuyerInfo())
                        .timestamp(event.getTimestamp())
                        .build());
            }

            ack.acknowledge();
        } catch (Exception e) {
            log.error("[consumePaymentHistoryHistoricalToUpdateInvoiceBuyerInfoRecent] Error when process message", e);
            ack.acknowledge();
        }
    }

    @KafkaListener(
            topics = "${kafka.historical.payment_history.topic.name}",
            groupId = "${kafka.historical.payment_history.create_invoice.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory"
    )
    public void consumePaymentHistoryHistoricalToCreateInvoice(ConsumerRecord<String, String> record, Acknowledgment ack) throws InterruptedException {
        log.info("[consumePaymentHistoryHistoricalToCreateInvoice][Kafka] Consumed message - topic={}, partition={}, offset={}, message={}",
                record.topic(), record.partition(), record.offset(), record.value());
        HistoricalEvent<PaymentHistory> event = null;
        try (Profiler p = new Profiler(getClass(), "consumePaymentHistoryHistoricalToCreateInvoice")) {
            String message = record.value();
            log.debug("[consumePaymentHistoryHistoricalToCreateInvoice] Start process message {}", message);

            event = JsonUtils.toObjectOrThrow(message, new TypeReference<>() {
            });

            if (event.getData() != null && Objects.equals(event.getData().getStatus(), PaymentStatusEnum.SUCCESS)
                    && event.getOldData() != null && !Objects.equals(event.getOldData().getStatus(), PaymentStatusEnum.SUCCESS)) {

                // create draft invoice with auto issue
                paymentService.createDraftPaymentInvoice(event.getData().getPaymentId(), InvoiceSourceEnum.AUTO);
            }

            ack.acknowledge();
        } catch (Exception e) {
            log.error("[consumePaymentHistoryHistoricalToCreateInvoice] Error when process message", e);
            ack.acknowledge();
        }
    }

    @KafkaListener(
            topics = "${kafka.issue_invoice.topic}",
            groupId = "${kafka.issue_invoice.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory"
    )
    public void consumeIssueInvoiceEvent(ConsumerRecord<String, String> record, Acknowledgment ack) throws InterruptedException {
        log.info("[consumeIssueInvoiceEvent][Kafka] Consumed message - topic={}, partition={}, offset={}, message={}",
                record.topic(), record.partition(), record.offset(), record.value());
        IssueInvoiceEvent event = null;
        try (Profiler p = new Profiler(getClass(), "consumeIssueInvoiceEvent")) {
            String message = record.value();
            log.debug("[consumeIssueInvoiceEvent] Start process message {}", message);

            event = JsonUtils.toObjectOrThrow(message, new TypeReference<>() {
            });

            invoiceService.issueInvoiceByEvent(event);

            ack.acknowledge();
        } catch (Exception e) {
            log.error("[consumeIssueInvoiceEvent] Error when process message", e);
            ack.nack(Duration.ofSeconds(5));
        }
    }


}
