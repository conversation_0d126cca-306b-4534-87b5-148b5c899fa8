package vn.vinclub.payment.controller.internal;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.dto.PaymentMethodDto;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.model.PaymentMethod;
import vn.vinclub.payment.service.PaymentMethodService;
import vn.vinclub.payment.service.PaymentService;
import vn.vinclub.payment.util.ServiceResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/internal/payment")
@Slf4j
@RequiredArgsConstructor
public class InternalPaymentController {
    private final PaymentService paymentService;
    private final PaymentMethodService paymentMethodService;

    @GetMapping("/histories/{paymentId}")
    public ServiceResponse<?> getHistories(@PathVariable String paymentId) {
        try (var p = new Profiler(getClass(), "getHistories")) {
            return ServiceResponse.success(paymentService.getFullPaymentHistoryByPaymentId(paymentId));
        }
    }

    @GetMapping("/histories")
    private ServiceResponse<?> filter(PaymentHistoryFilter filter, Pageable pageable) {
        try (var p = new Profiler(getClass(), "filter")) {
            return ServiceResponse.success(paymentService.getFullPaymentHistories(filter, pageable));
        }
    }

    @GetMapping("/payment-methods/mget")
    public ServiceResponse<List<PaymentMethodDto>> getPaymentMethods(String ids, String lang) {
        try (var p = new Profiler(getClass(), "getPaymentMethods")) {
            Set<Long> paymentIds = Arrays.stream(ids.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
            List<PaymentMethod> activePaymentMethods = paymentMethodService.getActivePaymentMethods();
            return ServiceResponse.success(activePaymentMethods.stream()
                    .filter(e -> paymentIds.contains(e.getId()))
                    .map(e -> PaymentMethodDto.toDto(e, lang, null, null))
                    .toList());
        }
    }

    @PostMapping("/{paymentId}/create-invoice")
    public ServiceResponse<?> createInvoice(@PathVariable String paymentId) {
        try (var p = new Profiler(getClass(), "createInvoice")) {
            paymentService.createDraftPaymentInvoice(paymentId, InvoiceSourceEnum.MANUAL);
            return ServiceResponse.success("Invoice created successfully for payment ID: " + paymentId);
        }
    }

}
