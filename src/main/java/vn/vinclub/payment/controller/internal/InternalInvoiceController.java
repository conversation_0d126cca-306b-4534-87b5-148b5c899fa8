package vn.vinclub.payment.controller.internal;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.dto.invoice.CreateInvoiceRequest;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfoConfigDto;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.util.ServiceResponse;

import java.util.List;

@RestController
@RequestMapping("/internal/invoices")
@Slf4j
@RequiredArgsConstructor
public class InternalInvoiceController {
    private final InvoiceService invoiceService;

    @GetMapping("/buyer-info/recent")
    public ServiceResponse<List<InvoiceBuyerInfo>> getRecentInvoiceBuyerInfos(Long customerId) {
        try (Profiler p = new Profiler(getClass(), "getRecentInvoiceBuyerInfos")) {
            return ServiceResponse.success(invoiceService.getRecentInvoiceBuyerInfos(customerId));
        }
    }

    @GetMapping("/buyer-info/configs")
    public ServiceResponse<InvoiceBuyerInfoConfigDto> getInvoiceBuyerConfigs(Long customerId) {
        try (Profiler p = new Profiler(getClass(), "getInvoiceBuyerConfigs")) {
            return ServiceResponse.success(invoiceService.getInvoiceBuyerConfigs(customerId));
        }
    }

    @PostMapping
    public ServiceResponse<String> createInvoice(CreateInvoiceRequest request) {
        try (Profiler p = new Profiler(getClass(), "createInvoice")) {
            invoiceService.createDraftInvoice(request);
            return ServiceResponse.success("Invoice created successfully");
        }
    }

    @PostMapping("/{invoiceId}/issue")
    public ServiceResponse<String> issueInvoice(@PathVariable Long invoiceId) {
        try (Profiler p = new Profiler(getClass(), "getInvoiceBuyerConfigs")) {
            invoiceService.processIssueInvoice(invoiceId);
            return ServiceResponse.success("Invoice issued successfully");
        }
    }

}
