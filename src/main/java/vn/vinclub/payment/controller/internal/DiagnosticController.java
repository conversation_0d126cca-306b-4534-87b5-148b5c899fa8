package vn.vinclub.payment.controller.internal;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.model.OutboxEvent;
import vn.vinclub.payment.repository.OutboxEventRepository;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Diagnostic controller for investigating JSON serialization issues
 */
@Slf4j
@RestController
@RequestMapping("/internal/diagnostic")
@RequiredArgsConstructor
public class DiagnosticController {

    private final ObjectMapper objectMapper;
    private final OutboxEventRepository outboxEventRepository;

    @GetMapping("/jackson-config")
    public Map<String, Object> getJacksonConfiguration() {
        Map<String, Object> config = new HashMap<>();
        
        // Jackson version info
        config.put("jacksonVersion", com.fasterxml.jackson.core.Version.unknownVersion().toString());
        
        // ObjectMapper features
        config.put("writeBigDecimalAsPlain", objectMapper.getFactory().isEnabled(
            com.fasterxml.jackson.core.JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN));
        
        // Test numeric serialization
        try {
            JsonNode testNode = JsonUtils.toNode(Map.of("testNumber", 3000000L));
            JsonNode numberNode = testNode.get("testNumber");
            config.put("testNumberType", numberNode.getNodeType().toString());
            config.put("testNumberValue", numberNode.toString());
            config.put("testNumberAsText", numberNode.asText());
            config.put("testNumberAsLong", numberNode.asLong());
        } catch (Exception e) {
            config.put("testError", e.getMessage());
        }
        
        return config;
    }

    @GetMapping("/outbox-payload-analysis")
    public Map<String, Object> analyzeOutboxPayload(@RequestParam String eventId) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            Optional<OutboxEvent> outboxEventOpt = outboxEventRepository.findByEventId(eventId);
            if (outboxEventOpt.isEmpty()) {
                analysis.put("error", "OutboxEvent not found for eventId: " + eventId);
                return analysis;
            }
            
            OutboxEvent outboxEvent = outboxEventOpt.get();
            JsonNode payload = outboxEvent.getPayload();
            
            analysis.put("eventId", eventId);
            analysis.put("payloadType", payload.getClass().getSimpleName());
            analysis.put("payloadString", payload.toString());
            
            // Analyze all numeric fields in the payload
            Map<String, Object> numericFields = new HashMap<>();
            analyzeJsonNodeForNumbers(payload, "", numericFields);
            analysis.put("numericFields", numericFields);
            
        } catch (Exception e) {
            log.error("Error analyzing outbox payload for eventId: {}", eventId, e);
            analysis.put("error", e.getMessage());
        }
        
        return analysis;
    }
    
    private void analyzeJsonNodeForNumbers(JsonNode node, String path, Map<String, Object> result) {
        if (node.isObject()) {
            node.fields().forEachRemaining(entry -> {
                String fieldPath = path.isEmpty() ? entry.getKey() : path + "." + entry.getKey();
                analyzeJsonNodeForNumbers(entry.getValue(), fieldPath, result);
            });
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                String arrayPath = path + "[" + i + "]";
                analyzeJsonNodeForNumbers(node.get(i), arrayPath, result);
            }
        } else if (node.isNumber()) {
            Map<String, Object> numberInfo = new HashMap<>();
            numberInfo.put("nodeType", node.getNodeType().toString());
            numberInfo.put("value", node.toString());
            numberInfo.put("asText", node.asText());
            numberInfo.put("asLong", node.asLong());
            numberInfo.put("asDouble", node.asDouble());
            numberInfo.put("isInt", node.isInt());
            numberInfo.put("isLong", node.isLong());
            numberInfo.put("isDouble", node.isDouble());
            numberInfo.put("isBigDecimal", node.isBigDecimal());
            result.put(path, numberInfo);
        }
    }
}
