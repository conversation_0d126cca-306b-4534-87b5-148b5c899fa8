package vn.vinclub.payment.controller.request.customer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import vn.vinclub.payment.enums.PaymentStatusEnum;

import java.util.List;

/**
 * <AUTHOR> 12/11/24 17:34
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetPaymentHistoryRequest {
    private List<PaymentStatusEnum> status;
}