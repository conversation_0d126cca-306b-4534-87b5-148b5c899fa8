package vn.vinclub.payment.controller.request.customer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.dto.CustomerInvoiceBuyerInfoDto;
import vn.vinclub.payment.dto.FeeDetail;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 12/11/24 17:29
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CheckoutPaymentRequest {

    private String retryWithPaymentId;

    @NotNull
    private TransactionTypeEnum transactionType;

    @Min(1)
    @NotNull
    private Long paymentMethodId;

    @Min(1)
    @NotNull
    private Long point;

    @Min(1)
    @NotNull
    private BigDecimal amount;

    @Builder.Default
    private BigDecimal feeAmount = BigDecimal.ZERO;

    @Builder.Default
    private List<FeeDetail> feeDetails = new ArrayList<>();

    @Builder.Default
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @Builder.Default
    private CurrencyEnum currency = CurrencyEnum.VND;

    @Builder.Default
    private BigDecimal totalAmount = BigDecimal.ZERO;

    private Long pointPerUnitAmount;

    private BigDecimal unitAmount;

    private CustomerInvoiceBuyerInfoDto invoiceInfo;

}
