package vn.vinclub.payment.controller.request.customer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import vn.vinclub.payment.enums.TransactionTypeEnum;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PaymentMethodRequest {
    private long point;
    private TransactionTypeEnum transactionCategory = TransactionTypeEnum.TOPUP_POINT;
}
