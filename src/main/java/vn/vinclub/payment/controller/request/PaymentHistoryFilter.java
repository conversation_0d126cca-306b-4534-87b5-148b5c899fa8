package vn.vinclub.payment.controller.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import vn.vinclub.payment.enums.InvoiceBuyerTypeEnum;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR> 12/25/24 15:52
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentHistoryFilter {

    private LocalDateTime createdOnFrom;
    private Long createdTimeFrom;

    private LocalDateTime createdOnTo;
    private Long createdTimeTo;

    private LocalDateTime updatedOnFrom;
    private Long updatedTimeFrom;

    private LocalDateTime updatedOnTo;
    private Long updatedTimeTo;

    private LocalDateTime paymentDateFrom;
    private Long paymentTimeFrom;

    private LocalDateTime paymentDateTo;
    private Long paymentTimeTo;

    @Enumerated(EnumType.STRING)
    private TransactionTypeEnum transactionType;

    @Enumerated(EnumType.STRING)
    private PaymentStatusEnum status;

    private List<PaymentStatusEnum> statuses;

    @Enumerated(EnumType.STRING)
    private PaymentMethodEnum paymentMethodType;

    @Enumerated(EnumType.STRING)
    private PaymentServiceProviderEnum serviceProviderType;

    @Enumerated(EnumType.STRING)
    private PaymentGatewayEnum pgwType;

    private String pgwTxnId;

    private Long ltId;

    private String paymentId;

    private Long customerId;
    private String email;
    private String phone;

    private String keyword;

    private Boolean requestInvoice;
    private InvoiceBuyerTypeEnum invoiceType;

    public void prepareData() {

        if (this.createdTimeFrom != null && this.createdTimeFrom > 0) {
            this.createdOnFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.createdTimeFrom), ZoneId.systemDefault());
        }

        if (this.createdTimeTo != null && this.createdTimeTo > 0) {
            this.createdOnTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.createdTimeTo), ZoneId.systemDefault());
        }

        if (this.updatedTimeFrom != null && this.updatedTimeFrom > 0) {
            this.updatedOnFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.updatedTimeFrom), ZoneId.systemDefault());
        }

        if (this.updatedTimeTo != null && this.updatedTimeTo > 0) {
            this.updatedOnTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.updatedTimeTo), ZoneId.systemDefault());
        }

        if (this.paymentTimeFrom != null && this.paymentTimeFrom > 0) {
            this.paymentDateFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.paymentTimeFrom), ZoneId.systemDefault());
        }

        if (this.paymentTimeTo != null && this.paymentTimeTo > 0) {
            this.paymentDateTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.paymentTimeTo), ZoneId.systemDefault());
        }

    }

}
