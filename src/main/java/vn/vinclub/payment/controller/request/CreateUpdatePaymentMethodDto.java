package vn.vinclub.payment.controller.request;

import lombok.Data;
import vn.vinclub.payment.dto.FeeConfig;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentMethodStatusEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 1/3/25 15:15
 */
@Data
public class CreateUpdatePaymentMethodDto {

    private PaymentMethodStatusEnum status;
    private PaymentMethodEnum paymentMethodType;
    private PaymentServiceProviderEnum serviceProviderType;
    private Long serviceProviderId;
    private PaymentGatewayEnum pgwType;
    private Long pgwId;
    private Integer displayOrder;
    private Map<String, String> displayNames;
    private Boolean feeRequired;
    private BigDecimal feePercentage;
    private BigDecimal fixedFee;
    private BigDecimal minAmountAllowed;
    private BigDecimal maxAmountAllowed;
    private List<FeeConfig> feeConfigs;

}
