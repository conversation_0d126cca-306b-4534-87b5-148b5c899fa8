package vn.vinclub.payment.controller.customer;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.controller.request.customer.GetPaymentHistoryRequest;
import vn.vinclub.payment.controller.request.customer.PaymentMethodRequest;
import vn.vinclub.payment.dto.CheckoutPreviewDto;
import vn.vinclub.payment.dto.CustomerPaymentConfigDto;
import vn.vinclub.payment.dto.CustomerPaymentHistoryDto;
import vn.vinclub.payment.dto.CustomerPaymentMethodDto;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.interceptor.RateLimiter;
import vn.vinclub.payment.mapper.PaymentHistoryMapper;
import vn.vinclub.payment.service.PaymentService;
import vn.vinclub.payment.service.impl.BaseService;
import vn.vinclub.payment.util.ServiceResponse;

import java.util.List;

/**
 * <AUTHOR> 12/11/24 17:12
 */
@RestController
@RequestMapping("/customer/payment")
@RequiredArgsConstructor
public class CustomerPaymentController extends BaseService {

    private final PaymentService paymentService;
    private final PaymentHistoryMapper paymentHistoryMapper;

    @Operation(summary = "Get payment config")
    @GetMapping("/configs")
    public ServiceResponse<CustomerPaymentConfigDto> getConfigs() {
        try (var p = new Profiler(getClass(), "getConfigs")) {
            return ServiceResponse.success(paymentService.getConfigs(getCurrVClubCustomer().getId()));
        }
    }

    @Operation(summary = "Get payment methods for the customer")
    @GetMapping("/payment-methods")
    public ServiceResponse<List<CustomerPaymentMethodDto>> getPaymentMethods(PaymentMethodRequest paymentMethodRequest) {
        try (var p = new Profiler(getClass(), "getPaymentMethods")) {
            var result = paymentService.getAvailablePaymentMethods(paymentMethodRequest)
                    .stream().map(i -> {
                        var r = new CustomerPaymentMethodDto();
                        BeanUtils.copyProperties(i, r);
                        return r;
                    })
                    .toList();
            return ServiceResponse.success(result);
        }
    }

    @PostMapping("/checkout")
    @RateLimiter(subjects = {RateLimiter.Subject.CUSTOMER, RateLimiter.Subject.API}, rate = 10, timeWindow = "PT1M", value = "payment-checkout")
    public ServiceResponse<CustomerPaymentHistoryDto> checkout(@RequestBody @Valid CheckoutPaymentRequest req) {
        try (var p = new Profiler(getClass(), "checkout")) {
            paymentService.validatePaymentQuota(req.getTransactionType(), getCurrVClubCustomer().getId());
            var result = paymentService.checkout(req);
            return ServiceResponse.success(paymentHistoryMapper.toCustomerPaymentHistoryDTO(result, getCurrVClubCustomerLang()));
        }
    }

    @PostMapping("/checkout/preview")
    @RateLimiter(subjects = {RateLimiter.Subject.CUSTOMER, RateLimiter.Subject.API}, rate = 10, timeWindow = "PT1M", value = "payment-checkout")
    public ServiceResponse<CheckoutPreviewDto> calculateCheckout(@RequestBody @Valid CheckoutPaymentRequest req) {
        try (var p = new Profiler(getClass(), "calculateCheckout")) {
            return ServiceResponse.success(paymentService.calculateCheckout(req));
        }
    }

    @GetMapping("/histories")
    public ServiceResponse<?> filterPaymentHistories(
            GetPaymentHistoryRequest req, Pageable pageable) {
        if (CollectionUtils.isEmpty(req.getStatus())) {
            req.setStatus(
                    List.of(
                            PaymentStatusEnum.PROCESSING,
                            PaymentStatusEnum.SUCCESS,
                            PaymentStatusEnum.CANCELLED,
                            PaymentStatusEnum.FAILED,
                            PaymentStatusEnum.REFUNDED));
        }
        var result = paymentService.getPaymentHistories(req, pageable);
        return ServiceResponse.success(result.map(e -> paymentHistoryMapper.toCustomerPaymentHistoryDTO(e, getCurrVClubCustomerLang())));
    }

    @GetMapping("/histories/{paymentId}")
    public ServiceResponse<?> getPaymentHistoryByPaymentId(@PathVariable String paymentId) {
        var result = paymentService.getPaymentHistoryByPaymentId(paymentId);
        return ServiceResponse.success(paymentHistoryMapper.toCustomerPaymentHistoryDTO(result, getCurrVClubCustomerLang()));
    }

}