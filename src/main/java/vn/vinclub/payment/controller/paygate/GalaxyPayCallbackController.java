package vn.vinclub.payment.controller.paygate;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.IOException;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.galaxypay.GPayIpnReqDto;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.service.PaymentGatewayService;
import vn.vinclub.payment.service.PaymentHistoryService;
import vn.vinclub.payment.util.ServiceResponse;

/**
 * <AUTHOR> 12/11/24 17:45
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/paygate/galaxypay/callback")
public class GalaxyPayCallbackController {

    private final PaymentGatewayService paymentGatewayService;
    private final PaymentHistoryService _paymentHistoryService;

    @PostMapping("/ipn")
    public ServiceResponse<?> callbackIPN(HttpServletRequest request, @RequestBody GPayIpnReqDto req) {


        try (var p = new Profiler(getClass(), "callbackIPN")) {

            log.info("callbackIPN {}\t{}", request.getQueryString(), JsonUtils.toString(req));

            if (paymentGatewayService.isValidIPN(req)) {
                var result = paymentGatewayService.processIPN(req);
                _paymentHistoryService.updatePaymentInfo(result.getPaymentId(), result, AppConst.IPN);
                return ServiceResponse.success(result.getPaymentId());
            }
            return ServiceResponse.error(AppErrorCode.INVALID_PARAM, "ipn");
        } catch (Exception e) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR, e.getMessage()));
        }
    }

    @GetMapping("/log")
    public ServiceResponse<?> callbackLog(HttpServletRequest request) {
        try (var p = new Profiler(getClass(), "log-Get")) {
            log.info("callbackLogGet {}\t{}", request.getQueryString(), getBody(request));
            return ServiceResponse.success(AppErrorCode.SUCCESS);
        }
    }

    @PostMapping("/log")
    public ServiceResponse<?> callbackLogPost(HttpServletRequest request) {
        try (var p = new Profiler(getClass(), "log-Post")) {
            log.info("callbackLogPost {}\t{}", request.getQueryString(), getBody(request));
            return ServiceResponse.success(AppErrorCode.SUCCESS);
        }
    }

    public static String getBody(HttpServletRequest request) {
        StringBuilder stringBuilder = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            return stringBuilder.toString();
        }
        return stringBuilder.toString();
    }
}