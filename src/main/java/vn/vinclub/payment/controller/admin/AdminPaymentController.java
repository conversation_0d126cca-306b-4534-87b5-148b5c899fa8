package vn.vinclub.payment.controller.admin;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.controller.request.CreateUpdatePaymentMethodDto;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.dto.PaymentMethodDto;
import vn.vinclub.payment.model.PaymentMethod;
import vn.vinclub.payment.service.PaymentMethodService;
import vn.vinclub.payment.service.PaymentService;
import vn.vinclub.payment.util.ServiceResponse;

@RestController
@RequestMapping("/admin/payment")
@RequiredArgsConstructor
public class AdminPaymentController {

    @Value("${vclub_exchange_rate.point.per.unit.amount}")
    private Long pointPerUnitAmount;
    @Value("${vclub_exchange_rate.unit.amount}")
    private BigDecimal unitAmount;

    private final PaymentService paymentService;
    private final PaymentMethodService paymentMethodService;

    @GetMapping("/histories")
    private ServiceResponse<?> filter(PaymentHistoryFilter filter, Pageable pageable) {
        try (var p = new Profiler(getClass(), "filter")) {

            return ServiceResponse.success(paymentService.getFullPaymentHistories(filter, pageable));
        }

    }

    @GetMapping("/payment-method/{id}")
    private ServiceResponse<?> getPaymentMethod(@PathVariable Long id) {
        try (var p = new Profiler(getClass(), "getPaymentMethod")) {
            PaymentMethod paymentMethod = paymentMethodService.getByIdAndActive(id);
            return ServiceResponse.success(PaymentMethodDto.toDto(paymentMethod, AppConst.DEFAULT_LANGUAGE, pointPerUnitAmount, unitAmount));
        }
    }

    @PutMapping("/payment-method/{id}")
    private ServiceResponse<?> updatePaymentMethod(@PathVariable Long id, @RequestBody CreateUpdatePaymentMethodDto updatePaymentMethodDto) {
        try (var p = new Profiler(getClass(), "updatePaymentMethod")) {
            PaymentMethod paymentMethod = paymentMethodService.updatePaymentMethod(id, updatePaymentMethodDto);
            return ServiceResponse.success(PaymentMethodDto.toDto(paymentMethod, AppConst.DEFAULT_LANGUAGE, pointPerUnitAmount, unitAmount));
        }
    }

}
