package vn.vinclub.payment.controller.admin;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.payment.dto.InvoiceFilter;
import vn.vinclub.payment.mapper.InvoiceMapper;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.util.ServiceResponse;

@RestController
@RequestMapping("/admin/invoices")
@RequiredArgsConstructor
public class AdminInvoiceController {

    private final InvoiceService invoiceService;
    private final InvoiceMapper invoiceMapper;

    @Profiler
    @GetMapping
    public ServiceResponse<?> filter(InvoiceFilter filter, Pageable pageable) {
        return ServiceResponse.success(invoiceService.getAllInvoicesByFilter(filter, pageable)
                .map(invoiceMapper::toAdminDto));
    }

    @Profiler
    @GetMapping("/{invoiceId}/download")
    public ServiceResponse<?> downloadInvoice(@PathVariable Long invoiceId) {
        return ServiceResponse.success(invoiceService.downloadInvoice(invoiceId));
    }
}
