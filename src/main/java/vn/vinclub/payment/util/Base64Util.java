package vn.vinclub.payment.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Base64Util {

    public static String encode(String plainText) {
        byte[] plainTextBytes = plainText.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(plainTextBytes);
    }

    public static String decode(String base64EncodedData) {
        byte[] base64DecodedBytes = Base64.getDecoder().decode(base64EncodedData);
        return new String(base64DecodedBytes, StandardCharsets.UTF_8);
    }

}
