package vn.vinclub.payment.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import lombok.extern.slf4j.Slf4j;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;

@Slf4j
public class PaymentUtil {

    public static String generateIdempotentKey(Long customerId, CheckoutPaymentRequest checkoutPaymentRequest) {
        return String.join(
                String.valueOf(customerId), "_",
                String.valueOf(checkoutPaymentRequest.getPaymentMethodId()), "_",
                checkoutPaymentRequest.getAmount().setScale(1, RoundingMode.HALF_DOWN).toString(), "_",
                String.valueOf(checkoutPaymentRequest.getPoint()), "_",
                computeHash(checkoutPaymentRequest)
        );
    }

    public static String computeHash(Object obj) {
        String json = JsonUtils.toString(obj);
        if (json == null) {
            json = obj.toString();
        }
        String hash = calculateSHA256(json);
        if (hash == null) return json;
        return hash;
    }

    public static String calculateSHA256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            return null;
        }

    }


}