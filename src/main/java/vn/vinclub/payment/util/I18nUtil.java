package vn.vinclub.payment.util;

import java.io.IOException;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.common.util.TemplateUtils;
import vn.vinclub.payment.constant.AppConst;


@Slf4j
@RequiredArgsConstructor
public class I18nUtil {

    public static String applyTemplate(String templateKey, Map<String, Object> values, String language) {
        // Determine the Locale from the language code
        Locale locale = getLocale(language);

        // Load the ResourceBundle for the determined locale
        ResourceBundle resourceBundle = ResourceBundle.getBundle("messages/message", locale);

        // Retrieve the template string from the ResourceBundle using the key
        String templateString = resourceBundle.getString(templateKey);

        try {
            return TemplateUtils.generateContent(templateString, values);
        } catch (IOException e) {
            log.error("applyTemplate({}, {}, {})", templateKey, JsonUtils.toString(values), language, e);
            return "";
        }

    }

    private static Locale getLocale(String language) {
        if (language == null || language.isEmpty()) {
            return Locale.of(AppConst.DEFAULT_LANGUAGE);
        }
        String[] parts = language.split("-");
        if (parts.length == 2) {
            return Locale.of(parts[0], parts[1]);
        } else {
            return Locale.of(language);
        }
    }

}
