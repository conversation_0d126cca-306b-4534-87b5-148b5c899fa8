package vn.vinclub.payment.util;

import org.apache.commons.lang3.StringUtils;
import vn.vinclub.payment.constant.AppConst;

import java.time.LocalDate;
import java.time.YearMonth;

public class TimeUtil {

    public static LocalDate getStartDateByLimitType(String type) {
        if (StringUtils.equals(type, AppConst.POPUP_POINT_LIMIT_TYPE.DAILY)) {
            return LocalDate.now();
        } else if (StringUtils.equals(type, AppConst.POPUP_POINT_LIMIT_TYPE.MONTLY)) {
            return YearMonth.now().atDay(1);
        } else {
            throw new UnsupportedOperationException("Unsupported POPUP_POINT_LIMIT_TYPE: " + type);
        }
    }

}
