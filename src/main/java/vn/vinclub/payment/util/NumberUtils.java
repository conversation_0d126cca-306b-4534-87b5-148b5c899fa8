package vn.vinclub.payment.util;

import vn.vinclub.payment.enums.CurrencyEnum;

import java.util.ArrayList;
import java.util.List;

public class NumberUtils {


    private static final String[] numberText = {"", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"};
    private static final String[] numberUnits = {"", " nghìn", " triệu", " tỷ", " nghìn tỷ", " triệu tỷ", " tỷ tỷ"};

    private static String readTriple(int number) {
        int hundred = number / 100;
        int tenUnit = number % 100;
        int ten = tenUnit / 10;
        int unit = tenUnit % 10;

        StringBuilder result = new StringBuilder();

        if (hundred > 0) {
            result.append(numberText[hundred]).append(" trăm");
            if (ten == 0 && unit > 0) result.append(" lẻ");
        }

        if (ten > 1) {
            result.append(" ").append(numberText[ten]).append(" mươi");
            if (unit == 1) result.append(" mốt");
            else if (unit == 5) result.append(" lăm");
            else if (unit > 0) result.append(" ").append(numberText[unit]);
        } else if (ten == 1) {
            result.append(" mười");
            if (unit == 1) result.append(" một");
            else if (unit == 5) result.append(" lăm");
            else if (unit > 0) result.append(" ").append(numberText[unit]);
        } else if (ten == 0 && unit > 0) {
            result.append(" ").append(numberText[unit]);
        }

        return result.toString().trim();
    }

    private static String getCurrencyName(CurrencyEnum currency) {
        switch (currency) {
            case VND:
                return "đồng";
            default:
                throw new IllegalArgumentException("Unsupported currency: " + currency);
        }
    }

    public static String convertToWords(long number, CurrencyEnum currency) {
        if (number == 0) return "Không " + getCurrencyName(currency);

        List<String> parts = new ArrayList<>();

        int unitIndex = 0;
        while (number > 0) {
            int part = (int) (number % 1000);
            if (part != 0) {
                String text = readTriple(part);
                if (!text.isEmpty()) {
                    parts.add(0, text + numberUnits[unitIndex]);
                }
            }
            number /= 1000;
            unitIndex++;
        }

        String result = String.join(" ", parts);
        // Viết hoa chữ cái đầu + thêm "đồng"
        return Character.toUpperCase(result.charAt(0)) + result.substring(1) + " " + getCurrencyName(currency);
    }

    public static void main(String[] args) {
        System.out.println(convertToWords(11000, CurrencyEnum.VND));
    }


}
