package vn.vinclub.payment.util;

import vn.vinclub.payment.dto.FeeConfig;
import vn.vinclub.payment.dto.FeeDetail;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class FeeCalculatorUtil {

    public static List<FeeDetail> calculateFees(BigDecimal amount, List<FeeConfig> feeConfigs) {
        if (feeConfigs == null || feeConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        List<FeeDetail> feeDetails = new ArrayList<>();

        for (FeeConfig feeConfig : feeConfigs) {
            if (!Objects.equals(feeConfig.getStatus(), FeeConfig.FeeStatus.ACTIVE)) {
                continue;
            }
            BigDecimal fee = calculateSingleFee(amount, feeConfig);

            feeDetails.add(new FeeDetail(feeConfig.getFeeType(), fee));
        }

        return feeDetails;
    }

    private static BigDecimal calculateSingleFee(BigDecimal amount, FeeConfig feeConfig) {
        BigDecimal fee = BigDecimal.ZERO;

        if (feeConfig.getFixedAmount() != null) {
            fee = fee.add(feeConfig.getFixedAmount());
        }
        if (feeConfig.getPercentage() != null) {
            BigDecimal percentFee = amount.multiply(feeConfig.getPercentage())
                    .divide(BigDecimal.valueOf(100), 10, Optional.ofNullable(feeConfig.getRoundingMode()).orElse(RoundingMode.HALF_UP));
            fee = fee.add(percentFee);
        }

        if (feeConfig.getMinAmount() != null && fee.compareTo(feeConfig.getMinAmount()) < 0) {
            fee = feeConfig.getMinAmount();
        }
        if (feeConfig.getMaxAmount() != null && fee.compareTo(feeConfig.getMaxAmount()) > 0) {
            fee = feeConfig.getMaxAmount();
        }

        if (feeConfig.getRoundingMode() != null && feeConfig.getScale() != null && feeConfig.getScale() >= 0) {
            fee = fee.setScale(feeConfig.getScale(), feeConfig.getRoundingMode());
        }

        return fee;
    }
}

