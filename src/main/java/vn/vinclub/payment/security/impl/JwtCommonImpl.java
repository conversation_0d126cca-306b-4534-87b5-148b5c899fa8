package vn.vinclub.payment.security.impl;

import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.time.Instant;
import java.util.Base64;

@Slf4j
@RequiredArgsConstructor
@Component
public class JwtCommonImpl {

    @Value("${vclub_customer.jwt.public_key_rs256:}")
    private String rs256PublicKey;
    private NimbusJwtDecoder jwtDecoder;
    private volatile Key publicKey;

    private final OAuth2TokenValidator<Jwt> validators = JwtValidators.createDefault();

    public JwtDecoder getJwtDecoder() {
        if (jwtDecoder != null) {
            return jwtDecoder;
        }

        jwtDecoder = NimbusJwtDecoder
                .withPublicKey((RSAPublicKey) getPublicKey())
                .build();
        return jwtDecoder;
    }


    public Key getPublicKey() {
        if (publicKey != null) {
            return publicKey;
        }
        try {
            String publicKey = rs256PublicKey;
            publicKey = publicKey
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll(System.lineSeparator(), "")
                    .trim();

            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));
            this.publicKey = KeyFactory.getInstance("RSA")
                    .generatePublic(x509EncodedKeySpec);
        } catch (Exception e) {
            log.error("ERROR", e);
            throw new RuntimeException(e);
        }
        return publicKey;
    }

    public Jwt decodeJwt(String token) {
        try {
            return getJwtDecoder().decode(token);
        } catch (Exception e) {
            log.error("ERROR", e);
            return null;
        }
    }

    public JwtDecoder getCmsJwtDecoder() {
        // Only validate time claims
        return token -> {
            try {

                // Parse the token
                SignedJWT signedJWT = SignedJWT.parse(token);

                // Extract claims without validation
                var claims = signedJWT.getJWTClaimsSet();
                Jwt jwt = new Jwt(
                        token,
                        claims.getIssueTime() != null ? claims.getIssueTime().toInstant() : Instant.now(),
                        claims.getExpirationTime() != null ? claims.getExpirationTime().toInstant() : Instant.MAX,
                        signedJWT.getHeader().toJSONObject(),
                        claims.getClaims()
                );
                validators.validate(jwt);
                return jwt;
            } catch (ParseException e) {
                throw new IllegalArgumentException("Invalid JWT token", e);
            }
        };
    }

    public Jwt decodeCmsJwt(String token) {
        return getCmsJwtDecoder().decode(token);
    }
}
