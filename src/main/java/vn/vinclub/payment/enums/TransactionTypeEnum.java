package vn.vinclub.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 15:49
 */
@Getter
@AllArgsConstructor
public enum TransactionTypeEnum {
    TOPUP_POINT("TOPUP_POINT"),
    REFUND("REFUND"),
    ;

    private String code;

    public static TransactionTypeEnum getValue(String code) {
        for (TransactionTypeEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }
}
