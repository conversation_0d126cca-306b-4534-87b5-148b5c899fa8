package vn.vinclub.payment.enums;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import vn.vinclub.payment.converter.PaymentStatusEnumCombinedSerializer;

/**
 * <AUTHOR> 12/11/24 14:25
 */
@JsonSerialize(using = PaymentStatusEnumCombinedSerializer.PaymentStatusEnumJsonSerializer.class)
@JsonDeserialize(using = PaymentStatusEnumCombinedSerializer.PaymentStatusEnumJsonDeserializer.class)
@Getter
@AllArgsConstructor
public enum PaymentStatusEnum {
    PROCESSING("PROCESSING"),
    SUCCESS("SUCCESS"),
    FAILED("FAILED"),
    CANCELLED("CANCELLED"),
    REFUNDED("REFUNDED"),
    ;

    private String code;

    public static PaymentStatusEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (PaymentStatusEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }

    public static boolean isCheckoutRetryable(PaymentStatusEnum status) {
        return status == CANCELLED || status == FAILED;
    }

    public static boolean triggerPointChange(PaymentStatusEnum oldStatus, PaymentStatusEnum status) {
        return status == SUCCESS || status == REFUNDED;
    }
}
