package vn.vinclub.payment.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;


@Getter
@AllArgsConstructor
public enum InvoiceProviderEnum {
    VNPT("VNPT"),
    ;

    private String code;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static InvoiceProviderEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (InvoiceProviderEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
