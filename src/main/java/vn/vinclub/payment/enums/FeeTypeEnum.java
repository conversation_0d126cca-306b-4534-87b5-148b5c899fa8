package vn.vinclub.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

@Getter
@AllArgsConstructor
public enum FeeTypeEnum {
    TRANSACTION("TRANSACTION", "transaction-fee-title", "transaction-fee-desc", "-"),
    PAYMENT("PAYMENT", "payment-fee-title", "payment-fee-desc", "-"),
    TAX("TAX", "vat-fee-title", "vat-fee-desc", "-"),
    PLATFORM("PLATFORM", "platform-fee-title", "platform-fee-desc", "Lần"),

    ;

    private final String code;
    private final String title;
    private final String description;
    private final String invoiceUnit;

    public static FeeTypeEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (FeeTypeEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }

}
