package vn.vinclub.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InvoiceTransactionTypeEnum {
    TOPUP_POINT("TOPUP_POINT"),
    TRANSFER_POINT("TRANSFER_POINT"),
    EXCHANGE_VOUCHER("EXCHANGE_VOUCHER"),
    ;

    private final String code;

    public static InvoiceTransactionTypeEnum getValue(String code) {
        for (InvoiceTransactionTypeEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

    public static InvoiceTransactionTypeEnum fromTransactionTypeEnum(TransactionTypeEnum transactionTypeEnum) {
        return switch (transactionTypeEnum) {
            case TOPUP_POINT -> TOPUP_POINT;
            // add more cases here
            default -> null;
        };
    }
}
