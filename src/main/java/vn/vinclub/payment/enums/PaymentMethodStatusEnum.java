package vn.vinclub.payment.enums;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 15:38
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodStatusEnum {

    ACTIVE("ACTIVE"),
    COMING_SOON("COMING_SOON"),
    INACTIVE("INACTIVE"),
    UNDER_MAINTENANCE("UNDER_MAINTENANCE"),
    ;

    private String code;

    public static PaymentMethodStatusEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (PaymentMethodStatusEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
