package vn.vinclub.payment.enums;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 14:25
 */
@Getter
@AllArgsConstructor
public enum CurrencyEnum {
    VND("VND"),
    ;

    private String code;

    public static CurrencyEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (CurrencyEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }

}
