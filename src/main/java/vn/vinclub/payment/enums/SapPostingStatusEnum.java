package vn.vinclub.payment.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;


@Getter
@AllArgsConstructor
public enum SapPostingStatusEnum {
    NOT_READY("NOT_READY"),
    WAITING("WAITING"),
    PROCESSING("PROCESSING"),
    POSTED("POSTED"),
    FAILED("FAILED"),
    ;

    private final String code;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static SapPostingStatusEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (SapPostingStatusEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
