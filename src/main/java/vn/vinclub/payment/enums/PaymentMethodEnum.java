package vn.vinclub.payment.enums;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 15:38
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodEnum {

    E_WALLET("E_WALLET"), // MoMo, ZaloPay, Viettel Money, ...
    QR_PAYMENT("QR_PAYMENT"), // Viet QR
    ATM_CARD("ATM_CARD"), // Customer enter atm info to transfer
    BANK_TRANSFER("BANK_TRANSFER"), // Show bank account number and waiting for customer transfer
    MOBILE_BANKING("MOBILE_BANKING"), // Customer use mobile banking to transfer
    DOMESTIC_CARD("DOMESTIC_CARD"),
    INTERNATIONAL_CARD("INTERNATIONAL_CARD"), // Visa, MasterCard, ...
    PAYMENT_GATEWAY("PAYMENT_GATEWAY"), // Direct to payment gateway: OnePay, VNPay, GalaxyPay, ...
    POINT_SWAP("POINT_SWAP"), // Use point to pay: SkyJoy, VinID, ...
    ;

    private String code;

    public static PaymentMethodEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (PaymentMethodEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
