package vn.vinclub.payment.enums;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 15:38
 */
@Getter
@AllArgsConstructor
public enum PaymentServiceProviderEnum {

    MOMO("MOMO"),
    ZALOPAY("ZALOPAY"),
    GALAXYPAY("GALAXYPAY"),
    VIETTEL("VIETTEL"),

    VISA("VISA"),
    MASTERCARD("MASTERCARD"),

    VIETQR("VIETQR"),

    SKYJOY("SKYJOY"),

    ;

    private String code;

    public static PaymentServiceProviderEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (PaymentServiceProviderEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
