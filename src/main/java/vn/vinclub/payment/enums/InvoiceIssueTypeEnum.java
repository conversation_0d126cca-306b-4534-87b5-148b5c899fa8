package vn.vinclub.payment.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;


@Getter
@AllArgsConstructor
public enum InvoiceIssueTypeEnum {
    ORIGINAL("ORIGINAL"),
    ADJUSTMENT("ADJUSTMENT"),
    REPLACEMENT("REPLACEMENT"),
    ADJUSTED("ADJUSTED"),
    REPLACED("REPLACED"),
    CANCELLED("CANCELLED"),
    ;

    private final String code;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static InvoiceIssueTypeEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (InvoiceIssueTypeEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
