package vn.vinclub.payment.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 18/11/24 12:57
 */
@Getter
@RequiredArgsConstructor
public enum OutboxStatusEnum {
    WAITING("WAITING"),
    SENT("SENT")
    ;

    public static final List<OutboxStatusEnum> values = Arrays.asList(OutboxStatusEnum.values());
    private final String code;

    public static OutboxStatusEnum getValue(String code) {
        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (OutboxStatusEnum e : values) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }

}
