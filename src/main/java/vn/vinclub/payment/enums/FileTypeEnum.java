package vn.vinclub.payment.enums;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import vn.vinclub.payment.converter.ImageTypeEnumCombinedSerializer;

@Getter
@AllArgsConstructor
@JsonDeserialize(using = ImageTypeEnumCombinedSerializer.ImageTypeEnumJsonDeserializer.class)
@JsonSerialize(using = ImageTypeEnumCombinedSerializer.ImageTypeEnumJsonSerializer.class)
public enum FileTypeEnum {

    THUMBNAIL,
    BANNER,
    AVATAR,
    IMAGE,
    FILE,

    LOGO
    ;

    public static FileTypeEnum getValue(String name) {

        if (ObjectUtils.isEmpty(name)) {
            return null;
        }

        for (FileTypeEnum e : FileTypeEnum.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }

        return null;
    }

}
