package vn.vinclub.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;


@Getter
@AllArgsConstructor
public enum InvoiceBuyerTypeEnum {
    BUSINESS("BUSINESS"),
    PERSONAL("PERSONAL"),
    ;

    private String code;

    public static InvoiceBuyerTypeEnum getValue(String code) {
        for (InvoiceBuyerTypeEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

    public static Set<InvoiceBuyerTypeEnum> getAll() {
        return Set.of(values());
    }
}
