package vn.vinclub.payment.enums;

import org.apache.commons.lang3.ObjectUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 12/11/24 15:38
 */
@Getter
@AllArgsConstructor
public enum PaymentGatewayEnum {

    GALAXY_PAY("GALAXY_PAY"),
    ;

    private String code;

    public static PaymentGatewayEnum getValue(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }

        for (PaymentGatewayEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }

        return null;
    }
}
