package vn.vinclub.payment.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvoiceSourceEnum {
    AUTO("AUTO"),
    MANUAL("MANUAL"),
    ;

    @JsonValue
    private final String code;

    @JsonCreator
    public static InvoiceSourceEnum getValue(String code) {
        for (InvoiceSourceEnum e : values()) {
            if (e.getCode().equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }
}
