package vn.vinclub.payment.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import vn.vinclub.payment.model.CustomerInvoiceBuyerInfo;

import java.util.Optional;

@Repository
public interface CustomerInvoiceBuyerInfoRepository extends JpaRepository<CustomerInvoiceBuyerInfo, Long>, JpaSpecificationExecutor<CustomerInvoiceBuyerInfo> {

    Optional<CustomerInvoiceBuyerInfo> findByCustomerId(Long customerId);

}
