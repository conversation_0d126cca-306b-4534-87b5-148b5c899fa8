package vn.vinclub.payment.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import vn.vinclub.payment.model.PaymentMethod;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentMethodRepository extends JpaRepository<PaymentMethod, Long> {

    List<PaymentMethod> findAllByActiveIsTrueOrderByDisplayOrder();

    Optional<PaymentMethod> findByIdAndActiveTrue(Long paymentMethodId);

    List<PaymentMethod> findByIdIn(Collection<Long> ids);

}
