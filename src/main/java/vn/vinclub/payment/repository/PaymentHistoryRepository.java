package vn.vinclub.payment.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.model.PaymentHistory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface PaymentHistoryRepository extends JpaRepository<PaymentHistory, Long>, JpaSpecificationExecutor<PaymentHistory> {

    PaymentHistory findFirstByCustomerIdAndStatusOrderByPaymentDateDesc(Long customerId, PaymentStatusEnum status);

    @Query(value = """
            select * from payment_history
            where active = true and status in (:#{#paymentStatus.![name()]}) and customer_id = :customerId and is_retry = false
            order by payment_date desc
            """, nativeQuery = true
    )
    Page<PaymentHistory> findCustomerPayments(@Param("paymentStatus") List<PaymentStatusEnum> paymentStatus,
                                              Long customerId, Pageable pageable);

    Optional<PaymentHistory> findOneByPaymentId(String paymentId);

    @Query(value = """
            select * from payment_history
            where active = true and id > :gtId and status = 'PROCESSING' and updated_on < :lteTime
            order by id asc
            limit :limit
            """, nativeQuery = true)
    List<PaymentHistory> findPendingPayments(LocalDateTime lteTime, int limit, long gtId);

    @Query(value = """
            SELECT COALESCE(SUM(CAST(jsonb_extract_path_text(data_request, 'point') as bigint)), 0)
            FROM payment_history
            WHERE active = true AND status in (:#{#paymentStatus.![name()]}) AND customer_id = :customerId
                  AND payment_date >= :startTime AND transaction_type = 'TOPUP_POINT'
            """, nativeQuery = true
    )
    Long sumTopupPointByCustomer(Long customerId, @Param("paymentStatus") List<PaymentStatusEnum> paymentStatus, LocalDateTime startTime);

}
