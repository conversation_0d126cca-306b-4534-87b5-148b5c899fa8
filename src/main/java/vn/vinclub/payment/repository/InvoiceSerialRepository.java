package vn.vinclub.payment.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.model.InvoiceSerial;

import java.util.Optional;

public interface InvoiceSerialRepository extends JpaRepository<InvoiceSerial, Long>, JpaSpecificationExecutor<InvoiceSerial> {

    @Query(value = """
            SELECT * FROM invoice_serials
            WHERE active is true and end_invoice_no > current_invoice_no and used_for = :usedFor
            ORDER BY priority
            LIMIT 1 OFFSET 0
            """, nativeQuery = true)
    Optional<InvoiceSerial> findValidSerial(String usedFor);

}
