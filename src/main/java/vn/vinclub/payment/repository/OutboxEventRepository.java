package vn.vinclub.payment.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.vinclub.payment.enums.OutboxStatusEnum;
import vn.vinclub.payment.model.OutboxEvent;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface OutboxEventRepository extends JpaRepository<OutboxEvent, Long> {

    List<OutboxEvent> findAllByStatusOrderByIdAsc(OutboxStatusEnum status);

    Optional<OutboxEvent> findByEventId(String outboxEventId);

}
