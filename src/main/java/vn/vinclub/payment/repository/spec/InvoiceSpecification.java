package vn.vinclub.payment.repository.spec;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import vn.vinclub.common.util.DateUtils;
import vn.vinclub.payment.dto.InvoiceFilter;
import vn.vinclub.payment.model.Auditable;
import vn.vinclub.payment.model.Invoice;

public class InvoiceSpecification {

    public static Specification<Invoice> buildFilter(InvoiceFilter filter) {
        filter.prepareData();
        return (root, query, builder) -> {

            Predicate predicate = builder.conjunction();

            predicate = builder.and(predicate, builder.isTrue(root.get(Invoice.Fields.active)));

            if (filter.getTransactionType() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.transactionType), filter.getTransactionType()));
            }
            if (filter.getTransactionId() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.transactionId), filter.getTransactionId()));
            }
            if (filter.getCustomerId() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.customerId), filter.getCustomerId()));
            }
            if (filter.getSource() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.source), filter.getSource()));
            }
            if (filter.getBrandId() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.brandId), filter.getBrandId()));
            }
            if (filter.getPatternNo() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.patternNo), filter.getPatternNo()));
            }
            if (filter.getSerialNo() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.serialNo), filter.getSerialNo()));
            }
            if (filter.getInvoiceNo() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.invoiceNo), filter.getInvoiceNo()));
            }

            if (filter.getProvider() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.provider), filter.getProvider()));
            }
            if (filter.getProviderUniqueId() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.providerUniqueId), filter.getProviderUniqueId()));
            }

            if (filter.getStatus() != null) {
                predicate = builder.and(predicate, builder.equal(root.get(Invoice.Fields.status), filter.getStatus()));
            }

            // list filter
            if (!CollectionUtils.isEmpty(filter.getStatuses())) {
                predicate = builder.and(predicate, root.get(Invoice.Fields.status).in(filter.getStatuses()));
            }

            boolean hasCreatedOnFrom = filter.getCreatedOnFrom() != null && DateUtils.getMilliseconds(filter.getCreatedOnFrom()) > 0;
            boolean hasCreatedOnTo = filter.getCreatedOnTo() != null && DateUtils.getMilliseconds(filter.getCreatedOnTo()) > 0;
            if (hasCreatedOnFrom && hasCreatedOnTo) {
                predicate = builder.and(predicate, builder.between(root.get(Auditable.Fields.createdOn), filter.getCreatedOnFrom(), filter.getCreatedOnTo()));
            } else if (hasCreatedOnFrom) {
                predicate = builder.and(predicate, builder.greaterThanOrEqualTo(root.get(Auditable.Fields.createdOn), filter.getCreatedOnFrom()));
            } else if (hasCreatedOnTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get(Auditable.Fields.createdOn), filter.getCreatedOnTo()));
            }

            boolean hasUpdatedOnFrom = filter.getUpdatedOnFrom() != null && DateUtils.getMilliseconds(filter.getUpdatedOnFrom()) > 0;
            boolean hasUpdatedOnTo = filter.getUpdatedOnTo() != null && DateUtils.getMilliseconds(filter.getUpdatedOnTo()) > 0;
            if (hasUpdatedOnFrom && hasUpdatedOnTo) {
                predicate = builder.and(predicate, builder.between(root.get(Auditable.Fields.updatedOn), filter.getUpdatedOnFrom(), filter.getUpdatedOnTo()));
            } else if (hasUpdatedOnFrom) {
                predicate = builder.and(predicate, builder.greaterThanOrEqualTo(root.get(Auditable.Fields.updatedOn), filter.getUpdatedOnFrom()));
            } else if (hasUpdatedOnTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get(Auditable.Fields.updatedOn), filter.getUpdatedOnTo()));
            }

            boolean hasIssuedOnFrom = filter.getIssuedOnFrom() != null && DateUtils.getMilliseconds(filter.getIssuedOnFrom()) > 0;
            boolean hasIssuedOnTo = filter.getIssuedOnTo() != null && DateUtils.getMilliseconds(filter.getIssuedOnTo()) > 0;
            if (hasIssuedOnFrom && hasIssuedOnTo) {
                predicate = builder.and(predicate, builder.between(root.get(Invoice.Fields.issuedAt), filter.getIssuedOnFrom(), filter.getIssuedOnTo()));
            } else if (hasIssuedOnFrom) {
                predicate = builder.and(predicate, builder.greaterThanOrEqualTo(root.get(Invoice.Fields.issuedAt), filter.getIssuedOnFrom()));
            } else if (hasIssuedOnTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get(Invoice.Fields.issuedAt), filter.getIssuedOnTo()));
            }

            return predicate;
        };
    }

}
