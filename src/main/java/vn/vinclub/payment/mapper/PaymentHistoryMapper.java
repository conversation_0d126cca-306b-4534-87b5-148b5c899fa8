package vn.vinclub.payment.mapper;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.dto.CheckoutPaymentDataRequest;
import vn.vinclub.payment.dto.CustomerPaymentHistoryDto;
import vn.vinclub.payment.dto.FeeDetail;
import vn.vinclub.payment.dto.FeeDetailDto;
import vn.vinclub.payment.dto.FullPaymentHistoryDto;
import vn.vinclub.payment.dto.PaymentHistoryDto;
import vn.vinclub.payment.model.PaymentHistory;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface PaymentHistoryMapper {

    FullPaymentHistoryDto toFullPaymentHistoryDTO(PaymentHistory paymentHistory);

    @Mapping(source = "feeDetails", target = "feeDetails", qualifiedByName = "toFeeDetailDtoList")
    @Mapping(source = "dataRequest", target = "dataRequest", qualifiedByName = "toCustomerDataRequestDto")
    CustomerPaymentHistoryDto toCustomerPaymentHistoryDTO(PaymentHistoryDto paymentHistoryDto, @Context String lang);

    @Named("toFeeDetailDtoList")
    default List<FeeDetailDto> toFeeDetailDtoList(List<FeeDetail> feeDetails, @Context String lang) {
        if (feeDetails == null) return Collections.emptyList();
        return feeDetails.stream()
                .map(fee -> toFeeDetailDto(fee, lang))
                .collect(Collectors.toList());
    }

    default FeeDetailDto toFeeDetailDto(FeeDetail feeDetail, String lang) {
        return FeeDetailDto.fromFeeDetail(feeDetail, lang);
    }

    @Named("toCustomerDataRequestDto")
    default ObjectNode toCustomerDataRequestDto(ObjectNode dataRequest) {
        return (ObjectNode) JsonUtils.toNodeSnakeCase(JsonUtils.treeToValue(dataRequest, CheckoutPaymentDataRequest.class));
    }
}
