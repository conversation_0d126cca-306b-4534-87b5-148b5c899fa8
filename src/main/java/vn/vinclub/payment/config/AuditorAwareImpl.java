package vn.vinclub.payment.config;

import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.context.SecurityContextHolder;
import vn.vinclub.payment.constant.AppConst;

import java.util.Optional;

public class AuditorAwareImpl implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {
        if (SecurityContextHolder.getContext().getAuthentication() == null) {
            return Optional.of(AppConst.UPDATED_BY_AUTO);
        }

        return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication().getName());
    }

}