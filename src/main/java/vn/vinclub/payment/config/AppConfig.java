package vn.vinclub.payment.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.core5.http.HttpHost;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.interceptor.RestTemplateLoggingInterceptor;
import vn.vinclub.payment.service.DistributedIdGenerator;
import vn.vinclub.payment.service.impl.SnowflakeNodeIdBasedRedis;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Configuration
@EnableRetry
@RequiredArgsConstructor
public class AppConfig {

    @Value("${webhook.request.timeout:45000}")
    private int requestTimeout;

    @Value("${proxy.host}")
    private String proxyHost;

    @Value("${proxy.port}")
    private int proxyPort;

    @Value("${vinclub.logging.level.client.RestTemplate:}")
    private String restTemplateLogLevel;

    private final RedissonClient redissonClient;
    private final ObjectMapper mapper;

    @Bean
    @Primary
    public RestTemplate restTemplate() {

        var requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(requestTimeout);
        requestFactory.setConnectionRequestTimeout(requestTimeout);

        RestTemplate restTemplate = new RestTemplate();
        if ("DEBUG".equalsIgnoreCase(restTemplateLogLevel)) {

            restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(requestFactory));

            List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
            if (CollectionUtils.isEmpty(interceptors)) {
                interceptors = new ArrayList<>();
            }
            interceptors.add(new RestTemplateLoggingInterceptor());

            restTemplate.setInterceptors(interceptors);

        } else {
            restTemplate.setRequestFactory(requestFactory);
        }

        return restTemplate;
    }

    @Bean
    @Qualifier("proxyRestTemplate")
    public RestTemplate proxyRestTemplate() {
        if (proxyHost == null || proxyHost.isEmpty() || proxyPort <= 0) {
            log.info("Proxy host or port not configured, using default RestTemplate");
            return restTemplate();
        }
        log.info("Init proxyRestTemplate host: {}, port: {}", proxyHost, proxyPort);
        HttpHost proxy = new HttpHost(proxyHost, proxyPort);
        HttpClient httpClient = HttpClientBuilder.create()
                .setProxy(proxy)
                .build();

        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setConnectTimeout(requestTimeout);
        factory.setConnectionRequestTimeout(requestTimeout);

        RestTemplate restTemplate = new RestTemplate(factory);
        if ("DEBUG".equalsIgnoreCase(restTemplateLogLevel)) {
            restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
            List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
            interceptors.add(new RestTemplateLoggingInterceptor());
            restTemplate.setInterceptors(interceptors);
        }

        return restTemplate;
    }

    @Bean("payment-id-gen")
    public DistributedIdGenerator paymentIdGenerator() {
        return new SnowflakeNodeIdBasedRedis(redissonClient, "payment-svc-payment-id-gen");
    }

    @Bean
    public BaseJsonUtils defaultBaseJsonUtils() {
        return new BaseJsonUtils(mapper);
    }

    @PostConstruct
    public void init() {
        JsonUtils.injectMapper(mapper);
    }
}
