package vn.vinclub.payment.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.service.OutboxEventService;
import vn.vinclub.payment.service.PaymentService;

/**
 * <AUTHOR> 12/18/24 07:43
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class CronJob {

    private final PaymentService _paymentService;
    private final OutboxEventService outboxEventService;
    private final InvoiceService invoiceService;

    @Scheduled(initialDelay = 60000, fixedDelay = 30000)
    @SchedulerLock(name = "updatePaymentStatusFromProcessing", lockAtLeastFor = "1m", lockAtMostFor = "5m")
    public void updatePaymentStatusFromProcessing() {
        log.info("Start updatePaymentStatusFromProcessing...........");
        _paymentService.syncPaymentStatusFromPaymentGateway(0L);
        log.info("End updatePaymentStatusFromProcessing!");
    }

    @Scheduled(initialDelay = 60000, fixedDelay = 30000)
    @SchedulerLock(name = "retryProcessPendingOutbox", lockAtLeastFor = "2m", lockAtMostFor = "10m")
    public void retryProcessPendingOutbox() {
        log.info("Start retryProcessPendingOutbox...........");
        outboxEventService.processPendingOutbox();
        log.info("End retryProcessPendingOutbox!");
    }

    @Scheduled(initialDelay = 60000, fixedDelay = 60000)
    @SchedulerLock(name = "scanAutoInvoiceToIssue", lockAtLeastFor = "1m", lockAtMostFor = "10m")
    public void scanAutoInvoiceToIssue() {
        log.info("Start scanAutoInvoiceToIssue...........");
        invoiceService.scanAutoInvoiceToIssue();
        log.info("End scanAutoInvoiceToIssue!");
    }

}
