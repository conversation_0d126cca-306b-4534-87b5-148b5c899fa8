package vn.vinclub.payment.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import vn.vinclub.payment.security.impl.CmsAuthenticationFilter;
import vn.vinclub.payment.security.impl.JwtCommonImpl;
import vn.vinclub.payment.security.impl.VClubCustomerAuthenticationFilter;

@EnableWebSecurity
@Configuration
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtCommonImpl jwtService;

    @Bean
    public SecurityFilterChain cmsAdminPaymentAuthenticationFilterChain(HttpSecurity http)
            throws Exception {
        http.securityMatcher(new AntPathRequestMatcher("/admin/**"))
                .csrf(AbstractHttpConfigurer::disable) // API calls, no need csrf
                .cors(AbstractHttpConfigurer::disable) // Handle this in API gateway
                .addFilterBefore(new CmsAuthenticationFilter(jwtService, new JwtAuthenticationConverter()), BasicAuthenticationFilter.class)
                .authorizeHttpRequests(authorizeRequests -> authorizeRequests
                        .anyRequest().authenticated())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        ;
        return http.build();
    }

    @Bean
    public SecurityFilterChain customerPaymentAuthenticationFilterChain(HttpSecurity http)
            throws Exception {
        http.securityMatcher(new AntPathRequestMatcher("/customer/**"))
                .csrf(AbstractHttpConfigurer::disable) // API calls, no need csrf
                .cors(AbstractHttpConfigurer::disable) // Handle this in API gateway
                .addFilterBefore(new VClubCustomerAuthenticationFilter(jwtService, new JwtAuthenticationConverter()), BasicAuthenticationFilter.class)
                .authorizeHttpRequests(authorizeRequests -> authorizeRequests
                        .anyRequest().authenticated())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        ;
        return http.build();
    }
}
