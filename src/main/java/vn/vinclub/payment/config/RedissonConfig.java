package vn.vinclub.payment.config;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.redis.spring.RedisLockProvider;

import org.apache.commons.lang3.ObjectUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;

@Configuration
public class RedissonConfig {

    @Value("${redisson.config.address}")
    private String address;

    @Value("${redisson.config.password}")
    private String password;

    @Value("${redisson.config.response.timeout:30}")
    private Integer responseTimeout;

    @Value("${redisson.config.connection.timeout:30}")
    private Integer connectionTimeout;

    @Value("${redisson.config.connection.idle.time:30}")
    private Integer idleTimeout;

    @Value("${redisson.config.connection.keep-alive:true}")
    private Boolean keepAlive;

    @Value("${redisson.config.connection.min:4}")
    private Integer connectionMinimumIdleSize;

    @Value("${redisson.config.connection.max:24}")
    private Integer connectionPoolSize;

    @Value("${redisson.config.database:0}")
    private Integer database;

    @Bean(destroyMethod = "shutdown")
    RedissonClient redisson() {
        Config redissonConfig = new Config();
        SingleServerConfig singleServerConfig = redissonConfig.useSingleServer();
        singleServerConfig.setConnectTimeout(connectionTimeout);
        singleServerConfig.setTimeout(responseTimeout);
        singleServerConfig.setIdleConnectionTimeout(idleTimeout);
        singleServerConfig.setKeepAlive(keepAlive);
        singleServerConfig.setConnectionMinimumIdleSize(connectionMinimumIdleSize);
        singleServerConfig.setConnectionPoolSize(connectionPoolSize);
        singleServerConfig.setAddress(address);
        singleServerConfig.setDatabase(database);
        if (!ObjectUtils.isEmpty(password)) {
            singleServerConfig.setPassword(password);
        }

        redissonConfig.setCodec(new StringCodec());
        return Redisson.create(redissonConfig);
    }

    @Bean
    @ConditionalOnMissingBean({RedisConnectionFactory.class})
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redisson) {
        return new RedissonConnectionFactory(redisson);
    }

    @Bean
    public LockProvider lockProvider(RedisConnectionFactory connectionFactory) {
        return new RedisLockProvider(connectionFactory);
    }

}
