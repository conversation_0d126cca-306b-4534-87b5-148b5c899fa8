package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.CurrencyEnum;

@Converter
public class CurrencyEnumConverter
    implements AttributeConverter<CurrencyEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(CurrencyEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public CurrencyEnum convertToEntityAttribute(String dbData) {

    return CurrencyEnum.getValue(dbData);
  }
}
