package vn.vinclub.payment.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import org.springframework.boot.jackson.JsonComponent;
import org.springframework.data.domain.Page;

import java.io.IOException;

@JsonComponent
public class PageableCombinedSerializer {

	public static class PageableJsonSerializer extends JsonSerializer<Page<?>> {

		@Override
		public void serialize(Page<?> value, JsonGenerator gen, SerializerProvider serializers)
				throws IOException {
			gen.writeStartObject();
			gen.writeObjectField("data", value.getContent());
			gen.writeNumberField("size", value.getSize());
			gen.writeNumberField("pageNum", value.getNumber());
			gen.writeNumberField("totalRecord", value.getTotalElements());
			gen.writeNumberField("totalPage", value.getTotalPages());
			gen.writeEndObject();
		}

	}

}
