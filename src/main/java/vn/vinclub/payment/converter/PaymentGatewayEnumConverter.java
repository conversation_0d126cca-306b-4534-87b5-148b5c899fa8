package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.PaymentGatewayEnum;

@Converter
public class PaymentGatewayEnumConverter
    implements AttributeConverter<PaymentGatewayEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(PaymentGatewayEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public PaymentGatewayEnum convertToEntityAttribute(String dbData) {

    return PaymentGatewayEnum.getValue(dbData);
  }
}
