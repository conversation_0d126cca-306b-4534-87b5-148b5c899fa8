package vn.vinclub.payment.converter;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentMethodStatusEnum;

@Converter
public class PaymentMethodStatusEnumConverter implements AttributeConverter<PaymentMethodStatusEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(PaymentMethodStatusEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public PaymentMethodStatusEnum convertToEntityAttribute(String dbData) {

        return PaymentMethodStatusEnum.getValue(dbData);
    }

}
