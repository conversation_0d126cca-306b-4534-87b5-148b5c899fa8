package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.InvoiceSourceEnum;

@Converter
public class InvoiceSourceEnumConverter implements AttributeConverter<InvoiceSourceEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(InvoiceSourceEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public InvoiceSourceEnum convertToEntityAttribute(String dbData) {

        return InvoiceSourceEnum.getValue(dbData);
    }

}
