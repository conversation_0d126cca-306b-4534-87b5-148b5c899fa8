package vn.vinclub.payment.converter;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.payment.enums.PaymentMethodEnum;

@Converter
public class PaymentMethodEnumConverter implements AttributeConverter<PaymentMethodEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(PaymentMethodEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public PaymentMethodEnum convertToEntityAttribute(String dbData) {

        return PaymentMethodEnum.getValue(dbData);
    }

}
