package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.InvoiceIssueTypeEnum;

@Converter
public class InvoiceIssueTypeEnumConverter implements AttributeConverter<InvoiceIssueTypeEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(InvoiceIssueTypeEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public InvoiceIssueTypeEnum convertToEntityAttribute(String dbData) {

    return InvoiceIssueTypeEnum.getValue(dbData);
  }
}
