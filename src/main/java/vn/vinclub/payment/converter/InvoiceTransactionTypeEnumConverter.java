package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.InvoiceTransactionTypeEnum;

@Converter
public class InvoiceTransactionTypeEnumConverter implements AttributeConverter<InvoiceTransactionTypeEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(InvoiceTransactionTypeEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public InvoiceTransactionTypeEnum convertToEntityAttribute(String dbData) {

        return InvoiceTransactionTypeEnum.getValue(dbData);
    }

}
