package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.OutboxStatusEnum;

/**
 * <AUTHOR> 18/11/24 12:57
 */
@Converter
public class OutboxStatusEnumConverter implements AttributeConverter<OutboxStatusEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(OutboxStatusEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public OutboxStatusEnum convertToEntityAttribute(String dbData) {

        return OutboxStatusEnum.getValue(dbData);
    }

}
