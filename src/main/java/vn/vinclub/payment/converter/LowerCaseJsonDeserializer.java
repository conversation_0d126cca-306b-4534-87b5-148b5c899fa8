package vn.vinclub.payment.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

public class LowerCaseJsonDeserializer extends JsonDeserializer<Object> {

	@Override
	public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {

		return p.hasToken(JsonToken.VALUE_STRING) ? p.getText().trim().toLowerCase() : null;
	}

}
