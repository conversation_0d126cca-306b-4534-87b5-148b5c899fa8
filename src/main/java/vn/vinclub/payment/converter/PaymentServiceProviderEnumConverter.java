package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;

@Converter
public class PaymentServiceProviderEnumConverter
    implements AttributeConverter<PaymentServiceProviderEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(PaymentServiceProviderEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public PaymentServiceProviderEnum convertToEntityAttribute(String dbData) {

    return PaymentServiceProviderEnum.getValue(dbData);
  }
}
