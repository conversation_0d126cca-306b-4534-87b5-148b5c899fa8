package vn.vinclub.payment.converter;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.payment.enums.TransactionTypeEnum;

@Converter
public class TransactionTypeEnumConverter implements AttributeConverter<TransactionTypeEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(TransactionTypeEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public TransactionTypeEnum convertToEntityAttribute(String dbData) {

        return TransactionTypeEnum.getValue(dbData);
    }

}
