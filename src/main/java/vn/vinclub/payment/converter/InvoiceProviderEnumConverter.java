package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.InvoiceProviderEnum;

@Converter
public class InvoiceProviderEnumConverter implements AttributeConverter<InvoiceProviderEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(InvoiceProviderEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public InvoiceProviderEnum convertToEntityAttribute(String dbData) {

    return InvoiceProviderEnum.getValue(dbData);
  }
}
