package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.SapPostingStatusEnum;

@Converter
public class SapPostingStatusEnumConverter implements AttributeConverter<SapPostingStatusEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(SapPostingStatusEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public SapPostingStatusEnum convertToEntityAttribute(String dbData) {

    return SapPostingStatusEnum.getValue(dbData);
  }
}
