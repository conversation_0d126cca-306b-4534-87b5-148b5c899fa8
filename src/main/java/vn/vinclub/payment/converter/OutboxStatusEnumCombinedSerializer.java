package vn.vinclub.payment.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;
import vn.vinclub.payment.enums.PaymentStatusEnum;

import java.io.IOException;

/**
 * <AUTHOR> 18/11/24 12:57
 */
@JsonComponent
public class OutboxStatusEnumCombinedSerializer {

    public static class PaymentStatusEnumJsonSerializer extends JsonSerializer<PaymentStatusEnum> {

        @Override
        public void serialize(PaymentStatusEnum value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            gen.writeString(value.getCode());
        }

    }

    public static class PaymentStatusEnumJsonDeserializer extends JsonDeserializer<PaymentStatusEnum> {

        @Override
        public PaymentStatusEnum deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            return p.hasToken(JsonToken.VALUE_STRING) ? PaymentStatusEnum.getValue(p.getText().trim())
                    : null;
        }

    }

}
