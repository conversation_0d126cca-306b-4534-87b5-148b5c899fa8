package vn.vinclub.payment.converter;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import vn.vinclub.payment.enums.PaymentStatusEnum;

@Converter
public class PaymentStatusEnumConverter implements AttributeConverter<PaymentStatusEnum, String>, BeanPostProcessor {

    @Override
    public String convertToDatabaseColumn(PaymentStatusEnum attribute) {

        return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
    }

    @Override
    public PaymentStatusEnum convertToEntityAttribute(String dbData) {

        return PaymentStatusEnum.getValue(dbData);
    }

}
