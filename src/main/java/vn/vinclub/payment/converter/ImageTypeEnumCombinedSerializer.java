package vn.vinclub.payment.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;
import vn.vinclub.payment.enums.FileTypeEnum;

import java.io.IOException;

@JsonComponent
public class ImageTypeEnumCombinedSerializer {

	public static class ImageTypeEnumJsonSerializer extends JsonSerializer<FileTypeEnum> {

		@Override
		public void serialize(FileTypeEnum value, JsonGenerator gen, SerializerProvider serializers)
				throws IOException {
			gen.writeString(value.name());
		}

	}

	public static class ImageTypeEnumJsonDeserializer extends JsonDeserializer<FileTypeEnum> {

		@Override
		public FileTypeEnum deserialize(JsonParser p, DeserializationContext ctxt)
				throws IOException {
			return p.hasToken(JsonToken.VALUE_STRING) ? FileTypeEnum.getValue(p.getText().trim()) : null;
		}

	}

}
