package vn.vinclub.payment.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.BeanPostProcessor;
import vn.vinclub.payment.enums.InvoiceIssueStatusEnum;

@Converter
public class InvoiceIssueStatusEnumConverter implements AttributeConverter<InvoiceIssueStatusEnum, String>, BeanPostProcessor {

  @Override
  public String convertToDatabaseColumn(InvoiceIssueStatusEnum attribute) {

    return ObjectUtils.isEmpty(attribute) ? null : attribute.getCode();
  }

  @Override
  public InvoiceIssueStatusEnum convertToEntityAttribute(String dbData) {

    return InvoiceIssueStatusEnum.getValue(dbData);
  }
}
