package vn.vinclub.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.enums.FeeTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FeeDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    private FeeTypeEnum feeType;
    private BigDecimal feeAmount;

}
