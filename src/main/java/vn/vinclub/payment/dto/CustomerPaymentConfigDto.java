package vn.vinclub.payment.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerPaymentConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long minPointAllowed;
    private Long maxPointAllowed;

    private Map<String, QuotaConfig> quotaConfigs;

    @Data
    @Builder
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class QuotaConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long remainingPoint;
        private Long limitPoint;
    }

}
