package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.net.URL;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.enums.FileTypeEnum;

@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SignedUrlInfoDto extends BaseDto {

	private static final long serialVersionUID = 1L;

	private URL url;

	private LocalDateTime expiredIn;

	@JsonAlias({"fileName", "imageName"})
	private String fileName;

	private FileTypeEnum type;

	private String format;

	private Boolean isPublic;

	private URL preview;

}
