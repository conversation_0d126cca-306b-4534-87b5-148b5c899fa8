package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.enums.InvoiceIssueStatusEnum;
import vn.vinclub.payment.enums.InvoiceProviderEnum;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.enums.InvoiceTransactionTypeEnum;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class InvoiceFilter {

    private InvoiceTransactionTypeEnum transactionType;
    private String transactionId;
    private Long customerId;
    private InvoiceSourceEnum source;

    private String brandId;
    private String patternNo;
    private String serialNo;
    private Long invoiceNo;

    private InvoiceProviderEnum provider;
    private String providerUniqueId;

    private InvoiceIssueStatusEnum status;

    private LocalDateTime createdOnFrom;
    private Long createdTimeFrom;

    private LocalDateTime createdOnTo;
    private Long createdTimeTo;

    private LocalDateTime updatedOnFrom;
    private Long updatedTimeFrom;

    private LocalDateTime updatedOnTo;
    private Long updatedTimeTo;

    private LocalDateTime issuedOnFrom;
    private Long issuedTimeFrom;

    private LocalDateTime issuedOnTo;

    private Long issuedTimeTo;


    // list filter
    private Set<InvoiceIssueStatusEnum> statuses;

    public void prepareData() {
        if (this.createdTimeFrom != null && this.createdTimeFrom > 0) {
            this.createdOnFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.createdTimeFrom), ZoneId.systemDefault());
        }

        if (this.createdTimeTo != null && this.createdTimeTo > 0) {
            this.createdOnTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.createdTimeTo), ZoneId.systemDefault());
        }

        if (this.updatedTimeFrom != null && this.updatedTimeFrom > 0) {
            this.updatedOnFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.updatedTimeFrom), ZoneId.systemDefault());
        }

        if (this.updatedTimeTo != null && this.updatedTimeTo > 0) {
            this.updatedOnTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.updatedTimeTo), ZoneId.systemDefault());
        }

        if (this.issuedTimeFrom != null && this.issuedTimeFrom > 0) {
            this.issuedOnFrom = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.issuedTimeFrom), ZoneId.systemDefault());
        }

        if (this.issuedTimeTo != null && this.issuedTimeTo > 0) {
            this.issuedOnTo = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.issuedTimeTo), ZoneId.systemDefault());
        }

    }
}
