package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.common.util.LangUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.model.PaymentMethod;

/**
 * <AUTHOR> 12/11/24 17:08
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@NoArgsConstructor
public class CustomerPaymentMethodDto extends PaymentMethodDto {

    @JsonProperty
    public Long paymentMethodId(){
        return this.getId();
    }

    @JsonProperty
    public PaymentMethodEnum type(){
        return this.getPaymentMethodType();
    }

}
