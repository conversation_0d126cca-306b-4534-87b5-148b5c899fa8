package vn.vinclub.payment.dto.galaxypay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 12/16/24 16:58
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
public class GPayResponseDto<T> {

    private String requestID;
    private String responseDateTime;
    private String responseCode;
    private String responseMessage;
    private T responseData;

    public boolean isSuccess() {
        return "200".equals(responseCode);
    }

}
