package vn.vinclub.payment.dto.galaxypay;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> 12/16/24 17:06
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
public class GPayReqPayData {

    private String apiOperation;

    private String orderId;

    @Builder.Default
    private String orderNumber = null;
    private BigDecimal orderAmount;
    private String orderCurrency;
    private String orderDateTime;
    private String orderDescription;

    private String paymentMethod;
    private String sourceType;

    private String extraData;

    @Builder.Default
    private String language = "vi";

    private String token;
    private String sourceOfFund;

    private String successURL;
    private String failureURL;
    private String cancelURL;
    private String ipnURL;

    @JsonIgnore
    public GPayReqPayData withApiOperationPay() {
        this.apiOperation = "PAY";
        return this;
    }

    @JsonIgnore
    public GPayReqPayData withApiOperationPayWithCreateToken() {
        this.apiOperation = "PAY_WITH_CREATE_TOKEN";
        return this;
    }

}
