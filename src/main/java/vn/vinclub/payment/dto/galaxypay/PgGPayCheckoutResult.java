package vn.vinclub.payment.dto.galaxypay;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.vinclub.payment.dto.PayGateCheckoutResult;

/**
 * <AUTHOR> 12/17/24 07:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PgGPayCheckoutResult extends PayGateCheckoutResult {

}
