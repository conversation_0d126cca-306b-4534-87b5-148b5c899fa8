package vn.vinclub.payment.dto.galaxypay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.uuid.Generators;

import lombok.Builder;
import lombok.Data;
import vn.vinclub.common.util.DateUtils;

/**
 * <AUTHOR> 12/16/24 16:58
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
public class GPayRequestDto<T> {

    @Builder.Default
    private String requestId = Generators.timeBasedEpochGenerator().generate().toString();

    @Builder.Default
    private String requestDateTime = DateUtils.convertDateToString(System.currentTimeMillis(), "yyyyMMddHHmmss");

    private T requestData;
}
