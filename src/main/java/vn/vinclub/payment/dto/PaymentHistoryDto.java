package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;
import vn.vinclub.payment.model.PaymentHistory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class PaymentHistoryDto {

    private String paymentId;

    private LocalDateTime paymentDate;

    private Long paymentMethodId;
    private String paymentMethodType;
    private String paymentMethodName;

    private String serviceProviderType;
    private Long serviceProviderId;

    private TransactionTypeEnum transactionType;

    private ObjectNode dataRequest;

    private CurrencyEnum currency;

    private PaymentStatusEnum status;

    private String paymentLink;

    private BigDecimal amount;
    private BigDecimal totalAmount;
    private BigDecimal feeAmount;
    private BigDecimal discountAmount;
    private List<FeeDetail> feeDetails;

    private String pgwType;
    private String pgwTxnId;
    private Long pgwId;

    public static PaymentHistoryDto toDto(PaymentHistory paymentHistory) {
        return PaymentHistoryDto.builder()
                .dataRequest(paymentHistory.getDataRequest())
                .currency(CurrencyEnum.VND)
                .paymentId(paymentHistory.getPaymentId())
                .transactionType(paymentHistory.getTransactionType())
                .paymentDate(paymentHistory.getPaymentDate())
                .status(paymentHistory.getStatus())

                .amount(paymentHistory.getAmount())
                .totalAmount(paymentHistory.getTotalAmount())
                .feeAmount(paymentHistory.getFeeAmount())
                .discountAmount(paymentHistory.getDiscountAmount())
                .feeDetails(paymentHistory.getFeeDetails())

                .paymentMethodId(paymentHistory.getPaymentMethodId())
                .paymentMethodType(Optional.ofNullable(paymentHistory.getPaymentMethodType()).map(PaymentMethodEnum::getCode).orElse(null))

                .serviceProviderId(paymentHistory.getServiceProviderId())
                .serviceProviderType(Optional.ofNullable(paymentHistory.getServiceProviderType()).map(PaymentServiceProviderEnum::getCode).orElse(null))

                .pgwType(Optional.ofNullable(paymentHistory.getPgwType()).map(PaymentGatewayEnum::getCode).orElse(null))
                .pgwTxnId(paymentHistory.getPgwTxnId())
                .pgwId(paymentHistory.getPgwId())


                .build();
    }

}
