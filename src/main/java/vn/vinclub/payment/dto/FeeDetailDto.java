package vn.vinclub.payment.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.enums.FeeTypeEnum;
import vn.vinclub.payment.util.I18nUtil;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FeeDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private FeeTypeEnum feeType;
    private String feeName;
    private String feeDescription;
    private BigDecimal feeAmount;

    public static FeeDetailDto fromFeeDetail(FeeDetail feeDetail, String lang) {
        return FeeDetailDto.builder()
                .feeType(feeDetail.getFeeType())
                .feeName(I18nUtil.applyTemplate(
                        feeDetail.getFeeType().getTitle(),
                        null,
                        lang))
                .feeDescription(I18nUtil.applyTemplate(
                        feeDetail.getFeeType().getDescription(),
                        null,
                        lang))
                .feeAmount(feeDetail.getFeeAmount())
                .build();
    }

}
