package vn.vinclub.payment.dto.event.outbox;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.model.PaymentHistory;

/**
 * <AUTHOR> 12/21/24 12:53
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
public class PaymentStatusChangedEvent implements BaseOutboxEvent {
    public static final String EVENT_CODE = "PAYMENT_STATUS_CHANGED";

    private Long timestamp;
    private PaymentHistory paymentHistory;
    private PaymentStatusEnum oldStatus;
    private PaymentStatusEnum status;

    @Override
    public String getKafkaMessageKey() {
        return String.valueOf(paymentHistory.getCustomerId());
    }

    @Override
    public String getEventCode() {
        return EVENT_CODE;
    }
}
