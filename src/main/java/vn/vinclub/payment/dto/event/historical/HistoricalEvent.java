package vn.vinclub.payment.dto.event.historical;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> 7/11/24 14:45
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HistoricalEvent<T> {

    private HistoricalActionEnum action;
    private Long timestamp;
    private String objectId;
    private T data;
    private T oldData;

    public HistoricalActionEnum getAction() {
        if (action != null) {
            return action;
        }

        if (data != null && oldData != null) {
            return HistoricalActionEnum.UPDATED;
        }

        if (data != null) {
            return HistoricalActionEnum.CREATED;
        }

        if (oldData != null) {
            return HistoricalActionEnum.DELETED;
        }

        return null;
    }
}