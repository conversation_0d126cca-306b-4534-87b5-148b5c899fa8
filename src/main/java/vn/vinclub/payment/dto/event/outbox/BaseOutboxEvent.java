package vn.vinclub.payment.dto.event.outbox;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;

public interface BaseOutboxEvent {
    default Long getTimestamp() {
        return System.currentTimeMillis();
    }

    @JsonIgnore
    default JsonNode getOutboxMetadata() {;
        return null;
    }

    @JsonIgnore
    String getKafkaMessageKey();

    String getEventCode();
}
