package vn.vinclub.payment.dto.event.outbox;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.SuperBuilder;
import vn.vinclub.payment.enums.InvoiceProviderEnum;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.enums.InvoiceTransactionTypeEnum;
import vn.vinclub.payment.model.Invoice;

import java.util.Objects;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class IssueInvoiceEvent implements BaseOutboxEvent {
    public static final String EVENT_CODE = "ISSUE_INVOICE";

    private Long invoiceId;
    private InvoiceTransactionTypeEnum transactionType;
    private String transactionId;
    private InvoiceSourceEnum source;

    private InvoiceProviderEnum provider;
    private String brandId;
    private String patternNo;
    private String serialNo;
    private Long invoiceNo;
    private Long timestamp;

    public boolean isSameWith(Invoice invoice) {
        return Objects.equals(invoiceId, invoice.getId()) &&
                Objects.equals(transactionType, invoice.getTransactionType()) &&
                Objects.equals(transactionId, invoice.getTransactionId()) &&
                Objects.equals(provider, invoice.getProvider()) &&
                Objects.equals(brandId, invoice.getBrandId()) &&
                Objects.equals(patternNo, invoice.getPatternNo()) &&
                Objects.equals(serialNo, invoice.getSerialNo()) &&
                Objects.equals(invoiceNo, invoice.getInvoiceNo());
    }

    @Override
    public String getKafkaMessageKey() {
        return String.valueOf(invoiceId);
    }

    @Override
    public String getEventCode() {
        return EVENT_CODE;
    }
}
