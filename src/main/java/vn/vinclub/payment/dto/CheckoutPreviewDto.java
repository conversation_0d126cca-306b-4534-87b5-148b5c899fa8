package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.enums.CurrencyEnum;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CheckoutPreviewDto {

    private Long paymentMethodId;
    private String paymentMethodName;

    private Long pointPerUnitAmount;
    private BigDecimal unitAmount;

    private CurrencyEnum currency;

    private BigDecimal amount;
    private BigDecimal totalAmount;
    private BigDecimal feeAmount;
    private List<FeeDetailDto> feeDetails;
    private BigDecimal discountAmount;

}
