package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FullPaymentHistoryDto {

    private Long id;

    private Integer version;

    private String createdBy;

    private String updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;

    @JsonProperty
    public Long getUpdatedTime() {
        return updatedOn != null ? updatedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    @JsonProperty
    public Long getCreatedTime() {
        return createdOn != null ? createdOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    private boolean active;

    private LocalDateTime deletedOn;

    @JsonProperty
    public Long getDeletedTime() {
        return deletedOn != null
                ? deletedOn.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                : null;
    }

    private String paymentId;

    private String originalPaymentId;

    private LocalDateTime paymentDate;

    @JsonProperty
    public Long getPaymentTime() {
        return paymentDate != null ? paymentDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
    }

    private PaymentStatusEnum status;

    private Long customerId;

    @JsonProperty
    public String customerIdStr() {
        return customerId != null ? String.valueOf(customerId) : null;
    }

    private TransactionTypeEnum transactionType;

    private BigDecimal amount;

    private BigDecimal feeAmount;

    private List<FeeDetail> feeDetails;

    private BigDecimal discountAmount;

    private BigDecimal totalAmount;

    private CurrencyEnum currency;

    private String description;

    private PaymentMethodEnum paymentMethodType;

    private Long paymentMethodId;

    private PaymentServiceProviderEnum serviceProviderType;

    private Long serviceProviderId;

    private PaymentGatewayEnum pgwType;

    private Long pgwId;

    private String pgwTxnId;

    private ObjectNode dataRequest;

    private ObjectNode pgwTxnResponse;

    private String pgwTxnResponseCode;

    private InvoiceBuyerInfo invoiceBuyerInfo;

    private boolean retry;
}
