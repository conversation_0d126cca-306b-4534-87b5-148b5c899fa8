package vn.vinclub.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.payment.enums.FeeTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeeConfig {

    private FeeTypeEnum feeType;
    private BigDecimal fixedAmount;
    private BigDecimal percentage;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;

    @Builder.Default
    private RoundingMode roundingMode = RoundingMode.HALF_UP;
    @Builder.Default
    private Integer scale = 2;

    @Builder.Default
    private FeeStatus status = FeeStatus.ACTIVE;

    public enum FeeStatus {
        ACTIVE,
        INACTIVE
    }
}
