package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> 12/11/24 17:08
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class BaseDto implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Long id;

    @JsonProperty("id_str")
    private String idStr() {
        return id == null ? null : String.valueOf(id);
    }

    private LocalDateTime createdOn;

    private String createdBy;

    private LocalDateTime updatedOn;

    private String updatedBy;

}
