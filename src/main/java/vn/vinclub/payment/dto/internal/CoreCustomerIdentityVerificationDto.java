package vn.vinclub.payment.dto.internal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreCustomerIdentityVerificationDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String customerId;
    private IdentityTypeEnum identityType;
    private String no;
    private CoreCustomerIdentityDocumentInfo customerIdentityInfo;
    private ApprovalStatus approvalStatus;
    private Long createdAt;
    private String createdBy;
    private Long updatedAt;
    private String updatedBy;
    private Long approvedAt;
    private String approvedBy;
}
