package vn.vinclub.payment.dto.internal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import vn.vinclub.payment.converter.LowerCaseJsonDeserializer;
import vn.vinclub.payment.dto.ClientInfoDto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreCustomerDto extends ClientInfoDto {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * vin id, customerId
     */
    private String originalId;
//    private String cdpId;

    /**
     * vp vinpearl id
     */
//    private String sourceSystemId;

    private String lastName;

    private String firstName;

    private String fullName;

//    private GenderEnum gender;

    // identity
//	private String citizenIdentity;
//
//	private String identityId;
//
//	private String passportId;
//
//	private String birthCertificate;
//
//	private String driverLicense;

    @Builder.Default
    private Map<String, String> identities = new HashMap<>();

    private String phoneNumber;
    private String originalPhoneNumber;

    //	@NotBlank
    @JsonDeserialize(using = LowerCaseJsonDeserializer.class)
    private String email;

    private Boolean isVerified;

    private String password;

    private LocalDate dob;

    private LocalDateTime lastOrderDate;

    private String originalDob;

    private String source;

    private String nationalityCode;

    private String companyName;

    private String jobPosition;

    private String jobTitle;

    private String companyPhone;

//    private List<AddressDto> addresses;

    private String username;

//    private String socialId1;

//    private String socialId2;

//    private String socialId3;

//    private AquisitionChannelEnum aquisitionChannel;

//    private String acquisitionChannel; //CRM[CDP]

//    private Boolean isOnlineCustomer;

//    private SignedUrlInfoDto avatarName;

//    private String originalAvatarName;

//    private String countryCode;

//    private String phoneCode;

//    private Long tenantId;

//    private CustomerStatusEnum status;

    private Long orgId;

    private String orgName;

    private String orgCode;

    private String metadata;

    private String referralCode;

    private String vfcwReferralCode;
    private String vfcwRank;
    private String vfcwDescription;

    private String gsmDriverReferralCode;

    private JsonNode extendField;

    private String referenceData;

    @Builder.Default
    private List<Long> orgIds = new ArrayList<>();

    private String key;

    private Long programId;

    private Boolean isCreate;

    private String profileId;

    private String[] otherIds;

    private String mainAccount;

    private Boolean isProfile;

    @JsonIgnore
    private String tierCode;

//    private List<CustomerMergeAccountResponseDto.MembershipDto> memberships = new ArrayList<>();

    private String sapId;

    @JsonDeserialize(using = LowerCaseJsonDeserializer.class)
    private String preferredLanguage;
    private String importedFrom;
    private Long referredById;
    private String referredBy;

    private Long createdAt;
    private Long updatedAt;
    private LocalDateTime sourceCreatedOn;
    private LocalDateTime sourceUpdatedOn;

//    private JsonNode pnlMapping;

    private int[] tierIds;

    private boolean identityDocumentVerified;

    public long getTierId() {
        if (tierIds != null && tierIds.length > 0) {
            return tierIds[tierIds.length - 1];
        }
        return 0;
    }

}
