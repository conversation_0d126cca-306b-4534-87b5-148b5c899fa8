package vn.vinclub.payment.dto.internal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import org.springframework.util.StringUtils;

import java.util.Optional;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.vinclub.common.util.PhoneUtils;

/**
 * <AUTHOR> 12/26/24 11:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreCustomerIdentifyRequest {

    public CoreCustomerIdentifyRequest(String phone, String email) {
        this.phone = phone;
        this.email = email;
    }

    private String pnl;

    @JsonProperty("pnl_profile_id")
    private String pnlProfileId = "";

    @JsonProperty("pnl_user_id")
    private String pnlUserId = "";

    @JsonProperty("vclub_user_id")
    private Long vclubUserId = 0L;

    @JsonProperty("phone")
    private String phone = "";

    @JsonProperty("email")
    private String email = "";

    @JsonProperty("identity_document_cccd_no")
    private String identityDocumentCccdNo = "";

    @JsonProperty("identity_document_passport_no")
    private String identityDocumentPassportNo = "";

    @Setter(AccessLevel.NONE)
    @JsonProperty("norm_phone")
    private String normPhone = null;

    public String getNormPhone() {
        if (normPhone != null) return normPhone;

        if (!StringUtils.hasText(phone)) {
            normPhone = "";
        } else {
            phone = phone.replace(" ", "+").trim();
            normPhone = PhoneUtils.normalizePhone(phone, PhoneUtils.REGION_VIETNAM).orElse(phone);
        }
        return normPhone;
    }

    public boolean isPnl(String _pnl) {
        return StringUtils.hasText(pnl) && pnl.equalsIgnoreCase(_pnl);
    }

    @JsonIgnore
    public boolean isOnlyFilterPhone() {
        return !StringUtils.hasText(pnlProfileId) &&
                !StringUtils.hasText(pnlUserId) &&
                Optional.ofNullable(vclubUserId).orElse(0L) <= 0L &&
                StringUtils.hasText(phone) &&
                !StringUtils.hasText(email) &&
                !StringUtils.hasText(identityDocumentCccdNo) &&
                !StringUtils.hasText(identityDocumentPassportNo);
    }

    @JsonIgnore
    public boolean isNotHaveIdentityToSearch() {
        return !StringUtils.hasText(pnlProfileId)
                && !StringUtils.hasText(pnlUserId)
                && Optional.ofNullable(vclubUserId).orElse(0L) <= 0L
                && !StringUtils.hasText(phone)
                && !StringUtils.hasText(email)
                && !StringUtils.hasText(identityDocumentCccdNo)
                && !StringUtils.hasText(identityDocumentPassportNo);
    }

}

