package vn.vinclub.payment.dto.internal;

public enum ApprovalStatus {
    WAITING, PROCESSING, SUCCESS, FAILED,
    UNDEF; // undefined value

    public static ApprovalStatus getValue(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (ApprovalStatus e : ApprovalStatus.values()) {
            if (e.name().equalsIgnoreCase(code)) {
                return e;
            }
        }
        return null;
    }

}
