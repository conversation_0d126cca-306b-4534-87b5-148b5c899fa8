package vn.vinclub.payment.dto.internal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CoreCustomerIdentityDocumentFilterDto {

    private String customerId;

    private String phone;

    private String email;

    private IdentityTypeEnum identityType;

    private String no;

    private ApprovalStatus approvalStatus;

    private Long createdAtStart;

    private Long createdAtEnd;

    private Long updatedAtStart;

    private Long updatedAtEnd;

    private String fullName;

    private String approvedBy;

    private Long dob;

    private String gender;

    private String nationality;

    private Long issuedDate;

    private String issuedBy;

    private Long expiredDate;

    private String all;
}