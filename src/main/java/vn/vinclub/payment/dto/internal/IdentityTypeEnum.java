package vn.vinclub.payment.dto.internal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
@AllArgsConstructor
public enum IdentityTypeEnum {
  CMND("CMND"),
  CCCD("CCCD"),
  PASSPORT("PASSPORT"),
  DRIVER_LICENSE("DRIVER_LICENSE"),
  TAX_CODE("TAX_CODE"),

  // undefined value
  UNDEF("-");

  private String code;

  public static IdentityTypeEnum getValue(String code) {

    if (StringUtils.isEmpty(code)) {
      return null;
    }

    for (IdentityTypeEnum e : IdentityTypeEnum.values()) {
      if (e.code.equalsIgnoreCase(code)) {
        return e;
      }
    }

    return null;
  }
}
