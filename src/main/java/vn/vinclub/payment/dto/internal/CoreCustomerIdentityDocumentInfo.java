package vn.vinclub.payment.dto.internal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreCustomerIdentityDocumentInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private IdentityTypeEnum identityType;
    private String no;

    private String fullName;
    private String dob;
    private String gender;
    private String issuedBy;
    private String issuedDate;
    private String expiredDate;
    private String address;
    private String region;
    private String city;
    private String nationality;
    private String hometown;
    private String ocrAddress;
}