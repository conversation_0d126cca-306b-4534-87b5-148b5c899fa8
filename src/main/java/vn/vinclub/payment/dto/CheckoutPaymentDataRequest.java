package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.math.BigDecimal;
import java.util.List;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class CheckoutPaymentDataRequest {
    private Long point;
    private Long pointPerUnitAmount;
    private BigDecimal unitAmount;
    private TransactionTypeEnum transactionType;

    private BigDecimal amount;
    private BigDecimal discountAmount;
    private BigDecimal feeAmount;
    private List<FeeDetail> feeDetails;
    private BigDecimal totalAmount;

    private String retryWithPaymentId;
    private CurrencyEnum currency;

    private BigDecimal feePercent;
    private BigDecimal feedFixed;

    public static CheckoutPaymentDataRequest fromCheckoutRequest(CheckoutPaymentRequest req) {
        return CheckoutPaymentDataRequest.builder()
                .point(req.getPoint())
                .pointPerUnitAmount(req.getPointPerUnitAmount())
                .unitAmount(req.getUnitAmount())
                .transactionType(req.getTransactionType())
                .amount(req.getAmount())
                .feeAmount(req.getFeeAmount())
                .feeDetails(req.getFeeDetails())
                .discountAmount(req.getDiscountAmount())
                .currency(req.getCurrency())
                .retryWithPaymentId(req.getRetryWithPaymentId())
                .build();
    }
}
