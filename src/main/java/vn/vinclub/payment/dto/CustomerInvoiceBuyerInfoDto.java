package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.enums.InvoiceBuyerTypeEnum;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Data
@Builder
public class CustomerInvoiceBuyerInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private InvoiceBuyerTypeEnum type;

    private String name;

    private String phone;

    private String email;

    private String address;

    private String taxCode;

    private String citizenId;

}
