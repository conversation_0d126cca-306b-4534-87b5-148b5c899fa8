package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import vn.vinclub.payment.enums.PaymentStatusEnum;

/**
 * <AUTHOR> 12/17/24 11:22
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
public class PayGatePaymentResult {

    private String paymentId;
    private LocalDateTime paymentDate;
    private String description;
    private PaymentStatusEnum status;

    private BigDecimal amount;
    private String currency;

    private String pgwTxnId;
    private String pgwTxnStage;
    private String pgwTxnDescription;
    private LocalDateTime pgwTxnDate;

}
