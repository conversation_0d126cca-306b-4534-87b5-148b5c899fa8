package vn.vinclub.payment.dto.invoice;

import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.enums.InvoiceTransactionTypeEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;
import vn.vinclub.payment.model.Invoice;

import java.util.List;

@Data
@Builder
public class CreateInvoiceRequest {
    private InvoiceTransactionTypeEnum transactionType;
    private String transactionId;
    private Long customerId;
    private InvoiceSourceEnum source;

    private InvoiceBuyerInfo buyerInfo;

    private List<Invoice.InvoiceProduct> items;
    private CurrencyEnum currency;

}
