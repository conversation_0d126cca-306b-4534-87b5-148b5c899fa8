package vn.vinclub.payment.dto.invoice;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.vinclub.payment.dto.CustomerInvoiceBuyerInfoDto;
import vn.vinclub.payment.enums.InvoiceBuyerTypeEnum;

import java.io.Serializable;
import java.util.Objects;

@SuperBuilder
@Data
@NoArgsConstructor
public class InvoiceBuyerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private InvoiceBuyerTypeEnum type;
    private String name;
    private String phone;
    private String email;
    private String address;
    private String taxCode;
    private String citizenId;

    private Long lastUsedTime; // Timestamp of the last time this invoice info was used

    public boolean isSame(InvoiceBuyerInfo other) {
        if (other == null) return false;
        return Objects.equals(type, other.type)
                && Objects.equals(name, other.name)
                && Objects.equals(phone, other.phone)
                && Objects.equals(email, other.email)
                && Objects.equals(address, other.address)
                && Objects.equals(taxCode, other.taxCode)
                && Objects.equals(citizenId, other.citizenId);
    }

    public static InvoiceBuyerInfo fromCustomerDto(CustomerInvoiceBuyerInfoDto dto) {
        if (dto == null) return null;
        return InvoiceBuyerInfo.builder()
                .type(dto.getType())
                .name(dto.getName())
                .phone(dto.getPhone())
                .email(dto.getEmail())
                .address(dto.getAddress())
                .taxCode(dto.getTaxCode())
                .citizenId(dto.getCitizenId())
                .build();
    }

}
