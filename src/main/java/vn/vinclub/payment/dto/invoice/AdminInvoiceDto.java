package vn.vinclub.payment.dto.invoice;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import vn.vinclub.payment.dto.BaseDto;
import vn.vinclub.payment.enums.*;
import vn.vinclub.payment.model.Invoice;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class AdminInvoiceDto extends BaseDto {

    private InvoiceTransactionTypeEnum transactionType;
    private String transactionId;
    private Long customerId;
    private InvoiceSourceEnum source;

    private String brandId;
    private String patternNo;
    private String serialNo;
    private Long invoiceNo;
    private LocalDateTime issuedAt;

    private InvoiceIssueStatusEnum status;
    private InvoiceIssueTypeEnum issueType;
    private boolean active;

    private InvoiceBuyerInfo buyerInfo;

    private List<Invoice.InvoiceProduct> items;

    private CurrencyEnum currency;
    private BigDecimal amount;
    private BigDecimal vatAmount;
    private BigDecimal totalAmount;

    private InvoiceProviderEnum provider;
    private String providerInvoiceId;
    private String providerRequest;
    private String providerResponse;

    private String sapId;
    private SapPostingStatusEnum sapPostingStatus;
    private String sapRequest;
    private String sapResponse;
    private JsonNode metadata;
}
