package vn.vinclub.payment.dto.invoice;

import lombok.Builder;
import lombok.Data;
import vn.vinclub.payment.enums.InvoiceBuyerTypeEnum;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Builder
@Data
public class InvoiceBuyerInfoConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<InvoiceConfig> configs;

    @Builder
    @Data
    public static class InvoiceConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        private InvoiceBuyerTypeEnum type;
        private Map<String, FieldConfig> fields;

    }

    @Builder
    @Data
    public static class FieldConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        private boolean required;
        private boolean editable;
        private String value;

    }
}
