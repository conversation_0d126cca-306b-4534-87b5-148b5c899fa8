package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.common.util.LangUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentMethodStatusEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.model.PaymentMethod;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 12/11/24 17:08
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class PaymentMethodDto {

    private Long id;

    private PaymentMethodEnum paymentMethodType;

    private PaymentMethodStatusEnum status;

    private PaymentServiceProviderEnum serviceProviderType;
    private Long serviceProviderId;

    private BigDecimal maxAmountAllowed;

    private BigDecimal minAmountAllowed;

    private boolean lastUsed;

    private Integer displayOrder;

    private boolean feeRequired;

    private BigDecimal feePercentage;

    private BigDecimal fixedFee;

    private String name;

    private Long pointPerUnitAmount;

    private BigDecimal unitAmount;

    @Builder.Default
    private CurrencyEnum currency = CurrencyEnum.VND;

    private List<FeeConfig> feeConfigs;

    public static PaymentMethodDto toDto(PaymentMethod paymentMethod, String lang, Long pointPerUnitAmount, BigDecimal unitAmount) {
        return PaymentMethodDto.builder()
                .id(paymentMethod.getId())
                .paymentMethodType(paymentMethod.getPaymentMethodType())
                .status(paymentMethod.getStatus())
                .serviceProviderType(paymentMethod.getServiceProviderType())
                .serviceProviderId(paymentMethod.getServiceProviderId())
                .minAmountAllowed(paymentMethod.getMinAmountAllowed())
                .maxAmountAllowed(paymentMethod.getMaxAmountAllowed())
                .displayOrder(paymentMethod.getDisplayOrder())
                .feeRequired(paymentMethod.getFeeRequired())
                .fixedFee(paymentMethod.getFixedFee())
                .feePercentage(paymentMethod.getFeePercentage())
                .name(LangUtils.getMapValueByLang(paymentMethod.getDisplayNames(), lang, AppConst.DEFAULT_LANGUAGE))
                .pointPerUnitAmount(pointPerUnitAmount)
                .unitAmount(unitAmount)
                .feeConfigs(paymentMethod.getFeeConfigs())
                .build();
    }
}
