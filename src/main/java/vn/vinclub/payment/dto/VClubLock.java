package vn.vinclub.payment.dto;

import java.util.concurrent.TimeUnit;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * <AUTHOR> 11/29/24 17:20
 */
public class VClubLock implements AutoCloseable {

    private final RLock transLock;

    public VClubLock(RedissonClient redisson, String lockName) {
        this(redisson.getLock(lockName));
    }

    public VClubLock(RLock transLock) {
        this(transLock, 10, 30, TimeUnit.SECONDS);
    }

    public VClubLock(RLock transLock, long waitTime, long leaseTime, TimeUnit unit) {
        this.transLock = transLock;
        try {
            if (!this.transLock.tryLock(waitTime, leaseTime, unit)) {
                throw new RuntimeException("Lock failed to lock");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() {
        if (this.transLock != null && this.transLock.isLocked() && this.transLock.isHeldByCurrentThread()) {
            this.transLock.unlock();
        }
    }
}
