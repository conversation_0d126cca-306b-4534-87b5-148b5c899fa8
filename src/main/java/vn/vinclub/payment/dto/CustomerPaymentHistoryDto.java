package vn.vinclub.payment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.Setter;
import vn.vinclub.payment.enums.CurrencyEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 12/19/24 17:05
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Getter
@Setter
public class CustomerPaymentHistoryDto {

    private String paymentId;

    private LocalDateTime paymentDate;

    private Long paymentMethodId;
    private String paymentMethodType;
    private String paymentMethodName;

    private String serviceProviderType;
    private Long serviceProviderId;

    private TransactionTypeEnum transactionType;

    private ObjectNode dataRequest;

    private CurrencyEnum currency;

    private PaymentStatusEnum status;

    private String paymentLink;

    private BigDecimal amount;
    private BigDecimal totalAmount;
    private BigDecimal feeAmount;
    private BigDecimal discountAmount;
    private List<FeeDetailDto> feeDetails;

    private String pgwType;
    private String pgwTxnId;
    private Long pgwId;

}
