package vn.vinclub.payment.interceptor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimiter {

    String value() default "DEFAULT_RATE_LIMITER";

    int rate() default 20;

    String timeWindow() default "PT1S";

    Subject[] subjects() default {};

    enum Subject {
        CUSTOMER,
        API,
    }

}
