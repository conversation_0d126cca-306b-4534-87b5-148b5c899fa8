package vn.vinclub.payment.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.dto.PayGateCheckoutResult;
import vn.vinclub.payment.dto.PayGatePaymentResult;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.model.PaymentHistory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface PaymentHistoryService {

    Optional<PaymentHistory> optLatestSuccessPaymentHistoryByCustomerId(Long customerId);

    Page<PaymentHistory> getPaymentHistoriesByStatus(List<PaymentStatusEnum> paymentStatus,
                                                     Long customerId, Pageable pageable);

    Optional<PaymentHistory> optPaymentHistoryByPaymentId(String paymentId);

    PaymentHistory getPaymentHistoryByPaymentId(String paymentId);

    PaymentHistory initCheckoutPayment(CheckoutPaymentRequest checkoutPaymentRequest, InvoiceBuyerInfo invoiceBuyerInfo);

    PaymentHistory updatePaymentInfoWithCheckoutResult(String paymentId, PayGateCheckoutResult checkoutResult);

    long countPaymentHistoriesByFilter(PaymentHistoryFilter filter);

    Long getPointTopupByCustomer(Long customerId, String type);

    void updatePaymentInfo(String paymentId, PayGatePaymentResult transactionDetail, String action) throws Exception;

    List<PaymentHistory> getPendingPayments(LocalDateTime lteTime, int limit, long gtId);

    Page<PaymentHistory> getFullPaymentHistories(PaymentHistoryFilter filter, Pageable pageable);

}
