package vn.vinclub.payment.service.impl;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.uuid.Generators;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.SerializationUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.DateUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.common.util.LStringUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppConst.REDIS_LOCK;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.dto.CheckoutPaymentDataRequest;
import vn.vinclub.payment.dto.PayGateCheckoutResult;
import vn.vinclub.payment.dto.PayGatePaymentResult;
import vn.vinclub.payment.dto.event.outbox.PaymentStatusChangedEvent;
import vn.vinclub.payment.dto.VClubLock;
import vn.vinclub.payment.dto.internal.CoreCustomerDto;
import vn.vinclub.payment.dto.internal.CoreCustomerIdentifyRequest;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.model.PaymentMethod;
import vn.vinclub.payment.repository.PaymentHistoryRepository;
import vn.vinclub.payment.service.*;
import vn.vinclub.payment.util.ServiceResponse;
import vn.vinclub.payment.util.TimeUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentHistoryServiceImpl extends BaseService implements PaymentHistoryService {

    @Value("${spring.application.name}")
    private String appName;
    @Value("${config.point-topup-by-customer.ttl_second:900}")
    private Long pointTopupByCustomerTtl;

    private final PaymentHistoryRepository _paymentHistoryRepository;
    private final PaymentMethodService paymentMethodService;
    private final OutboxEventService outboxEventService;
    private final PlatformTransactionManager transactionManager;
    private final RedissonClient redissonClient;
    private final LogHistoricalService _logHistoricalService;
    private final InternalService internalService;

    @Qualifier("payment-id-gen")
    private final DistributedIdGenerator paymentIdGenerator;

    @Override
    @Transactional(readOnly = true)
    public Optional<PaymentHistory> optLatestSuccessPaymentHistoryByCustomerId(Long customerId) {
        try (var p = new Profiler(getClass(), "optLatestSuccessPaymentHistoryByCustomerId")) {
            return Optional.ofNullable(
                    _paymentHistoryRepository.findFirstByCustomerIdAndStatusOrderByPaymentDateDesc(
                            customerId, PaymentStatusEnum.SUCCESS));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentHistory> getPaymentHistoriesByStatus(List<PaymentStatusEnum> paymentStatus,
                                                            Long customerId, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getPaymentHistoriesByStatus")) {
            return _paymentHistoryRepository.findCustomerPayments(paymentStatus, customerId, pageable);
        }
    }

    @Override
    public Optional<PaymentHistory> optPaymentHistoryByPaymentId(String paymentId) {
        try (var p = new Profiler(getClass(), "optPaymentHistoryByPaymentId")) {
            return _paymentHistoryRepository.findOneByPaymentId(paymentId);
        }
    }

    @Override
    public PaymentHistory getPaymentHistoryByPaymentId(String paymentId) {
        try (var p = new Profiler(getClass(), "getPaymentHistoryByPaymentId")) {
            return optPaymentHistoryByPaymentId(paymentId)
                    .orElseThrow(() -> new BusinessLogicException(
                            ServiceResponse.error(AppErrorCode.PAYMENT_HISTORY_NOT_FOUND)));
        }
    }

    @Override
    @Transactional
    public PaymentHistory initCheckoutPayment(CheckoutPaymentRequest checkoutPaymentRequest, InvoiceBuyerInfo invoiceBuyerInfo) {
        try (var p = new Profiler(getClass(), "initCheckoutPayment")) {

            PaymentMethod paymentMethod = paymentMethodService.getByIdAndActive(checkoutPaymentRequest.getPaymentMethodId());

            if (Objects.nonNull(checkoutPaymentRequest.getRetryWithPaymentId())) {

                var retryTxn = getPaymentHistoryByPaymentId(checkoutPaymentRequest.getRetryWithPaymentId());

                PaymentHistory oldTransactionClone = SerializationUtils.clone(retryTxn);
                if (!PaymentStatusEnum.isCheckoutRetryable(retryTxn.getStatus())) {
                    log.warn("Cannot retry txn {}, current status is {}", retryTxn.getPaymentId(), retryTxn.getStatus());
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_HISTORY_NO_RETRYABLE));
                }

                retryTxn.setRetry(true);
                retryTxn = _paymentHistoryRepository.save(retryTxn);
                _logHistoricalService.logHistoricalEvent(oldTransactionClone, retryTxn);

            }

            PaymentHistory ph = initDraftPaymentHistory(getCurrVClubCustomer().getId(), checkoutPaymentRequest,
                    paymentMethod,
                    checkoutPaymentRequest.getRetryWithPaymentId(),
                    invoiceBuyerInfo
            );

            ph = _paymentHistoryRepository.save(ph);

            invalidatePointTopupByCustomerCache(ph.getCustomerId());

            _logHistoricalService.logHistoricalEvent(null, ph);
            return ph;
        }
    }

    @Override
    @Transactional
    public PaymentHistory updatePaymentInfoWithCheckoutResult(String paymentId, PayGateCheckoutResult checkoutResult) {

        try (Profiler p = new Profiler(getClass(), "updatePaymentInfoAfterGeneratePaymentLink")) {

            try (var l = new VClubLock(redissonClient, String.format("%s_%s_%s", appName, REDIS_LOCK.TRANSACTION_HISTORY_CUD, paymentId))) {

                var paymentHistory = optPaymentHistoryByPaymentId(paymentId).orElseThrow(
                        () -> new BusinessLogicException(
                                ServiceResponse.error(AppErrorCode.PAYMENT_HISTORY_NOT_FOUND)));
                PaymentHistory oldPaymentHistory = SerializationUtils.clone(paymentHistory);

                ObjectNode gatewayResponse = Objects.isNull(paymentHistory.getPgwTxnResponse())
                        ? JsonNodeFactory.instance.objectNode() : paymentHistory.getPgwTxnResponse();
                gatewayResponse.put(AppConst.CHECKOUT_URL, checkoutResult.getCheckoutUrl());
                paymentHistory.setPgwTxnId(checkoutResult.getPgwTxnId());
                paymentHistory.setPgwTxnResponse(gatewayResponse);
                paymentHistory = _paymentHistoryRepository.save(paymentHistory);

                invalidatePointTopupByCustomerCache(paymentHistory.getCustomerId());
                _logHistoricalService.logHistoricalEvent(oldPaymentHistory, paymentHistory);

                return paymentHistory;
            }
        }

    }

    @Override
    @Transactional(readOnly = true)
    public List<PaymentHistory> getPendingPayments(LocalDateTime lteTime, int limit, long gtId) {
        try (var p = new Profiler(getClass(), "getPendingPayments")) {
            return _paymentHistoryRepository.findPendingPayments(lteTime, limit, gtId);
        }
    }

    @Override
    public Page<PaymentHistory> getFullPaymentHistories(PaymentHistoryFilter filter, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getFullPaymentHistories")) {
            Pageable _pageable = pageable;
            if (null == _pageable.getSort() || _pageable.getSort().isUnsorted()) {
                _pageable = PageRequest.of(_pageable.getPageNumber(), _pageable.getPageSize(),
                        Sort.by(Sort.Order.desc("id"))
                );
            }

            filter.prepareData();
            if (StringUtils.isNotEmpty(filter.getPhone()) || StringUtils.isNotEmpty(filter.getEmail())) {
                CoreCustomerDto customer = internalService.optCustomer(new CoreCustomerIdentifyRequest(filter.getPhone(), filter.getEmail())).orElse(null);
                if (customer == null) {
                    return Page.empty();
                }
                filter.setCustomerId(customer.getId());
            }
            Specification<PaymentHistory> specification = buildFilter(filter);

            return _paymentHistoryRepository.findAll(specification, _pageable);
        }
    }

    @Override
    public long countPaymentHistoriesByFilter(PaymentHistoryFilter filter) {
        try (var p = new Profiler(getClass(), "countPaymentHistoriesByFilter")) {
            filter.prepareData();
            Specification<PaymentHistory> specification = buildFilter(filter);

            return _paymentHistoryRepository.count(specification);
        }
    }

    @Override
    public Long getPointTopupByCustomer(Long customerId, String type) {
        try (var p = new Profiler(getClass(), "getPointTopupByCustomer")) {
            LocalDate startDate = TimeUtil.getStartDateByLimitType(type);
            RMapCache<Long, Long> pointTopupByCustomerId = redissonClient.getMapCache(String.format(AppConst.REDIS_KEY.POINT_TOPUP_BY_CUSTOMER_ID, type, startDate), AppConst.LONG_CODEC);
            Long cachedValue = pointTopupByCustomerId.get(customerId);
            if (cachedValue != null) {
                try (var p1 = new Profiler(p, "cacheHit")) {
                    return cachedValue;
                }
            }

            try (var p2 = new Profiler(p, "cacheMiss")) {
                Long dbValue = _paymentHistoryRepository.sumTopupPointByCustomer(customerId, Arrays.asList(PaymentStatusEnum.SUCCESS, PaymentStatusEnum.PROCESSING), startDate.atStartOfDay());

                if (dbValue != null) {
                    pointTopupByCustomerId.put(customerId, dbValue, pointTopupByCustomerTtl, TimeUnit.SECONDS);
                }

                return dbValue;
            }
        }
    }

    private void invalidatePointTopupByCustomerCache(Long customerId) {
        try (var p = new Profiler(getClass(), "invalidatePointTopupByCustomerCache")) {
            RMapCache<Long, Long> pointTopupByCustomerIdInDay = redissonClient.getMapCache(String.format(AppConst.REDIS_KEY.POINT_TOPUP_BY_CUSTOMER_ID, AppConst.POPUP_POINT_LIMIT_TYPE.DAILY, TimeUtil.getStartDateByLimitType(AppConst.POPUP_POINT_LIMIT_TYPE.DAILY)), AppConst.LONG_CODEC);
            pointTopupByCustomerIdInDay.remove(customerId);
            RMapCache<Long, Long> pointTopupByCustomerIdInMonth = redissonClient.getMapCache(String.format(AppConst.REDIS_KEY.POINT_TOPUP_BY_CUSTOMER_ID, AppConst.POPUP_POINT_LIMIT_TYPE.MONTLY, TimeUtil.getStartDateByLimitType(AppConst.POPUP_POINT_LIMIT_TYPE.MONTLY)), AppConst.LONG_CODEC);
            pointTopupByCustomerIdInMonth.remove(customerId);
        }
    }

    private Specification<PaymentHistory> buildFilter(PaymentHistoryFilter filter) {

        return (root, query, builder) -> {

            Predicate predicate = builder.conjunction();

            predicate = builder.and(predicate, builder.isTrue(root.get("active")));

            if (filter.getCustomerId() != null) {
                predicate = builder.and(predicate, root.get("customerId").in(filter.getCustomerId()));
            }

            if (StringUtils.isNotEmpty(filter.getPaymentId())) {
                predicate = builder.and(predicate, root.get("paymentId").in(filter.getPaymentId()));
            }

            if (filter.getLtId() != null && filter.getLtId() > 0) {
                predicate = builder.and(predicate, builder.lt(root.get("id"), filter.getLtId()));
            }

            if (filter.getStatus() != null) {
                predicate = builder.and(predicate, root.get("status").in(filter.getStatus()));
            }

            if (!CollectionUtils.isEmpty(filter.getStatuses())) {
                predicate = builder.and(predicate, root.get("status").in(filter.getStatuses()));
            }

            if (filter.getTransactionType() != null) {
                predicate = builder.and(predicate, root.get("transactionType").in(filter.getTransactionType()));
            }

            if (filter.getPaymentMethodType() != null) {
                predicate = builder.and(predicate, root.get("paymentMethodType").in(filter.getPaymentMethodType()));
            }

            if (filter.getServiceProviderType() != null) {
                predicate = builder.and(predicate, root.get("serviceProviderType").in(filter.getServiceProviderType()));
            }

            if (filter.getPgwType() != null) {
                predicate = builder.and(predicate, root.get("pgwType").in(filter.getPgwType()));
            }

            if (StringUtils.isNotEmpty(filter.getPgwTxnId())) {
                predicate = builder.and(predicate, root.get("pgwTxnId").in(filter.getPgwTxnId()));
            }

            boolean hasCreatedOnFrom = filter.getCreatedOnFrom() != null && DateUtils.getMilliseconds(filter.getCreatedOnFrom()) > 0;
            boolean hasCreatedOnTo = filter.getCreatedOnTo() != null && DateUtils.getMilliseconds(filter.getCreatedOnTo()) > 0;
            if (hasCreatedOnFrom && hasCreatedOnTo) {
                predicate = builder.and(predicate, builder.between(root.get("createdOn"), filter.getCreatedOnFrom(), filter.getCreatedOnTo()));
            } else if (hasCreatedOnFrom) {
                predicate = builder.and(predicate, builder.greaterThanOrEqualTo(root.get("createdOn"), filter.getCreatedOnFrom()));
            } else if (hasCreatedOnTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get("createdOn"), filter.getCreatedOnTo()));
            }

            boolean hasUpdatedOnFrom = filter.getUpdatedOnFrom() != null && DateUtils.getMilliseconds(filter.getUpdatedOnFrom()) > 0;
            boolean hasUpdatedOnTo = filter.getUpdatedOnTo() != null && DateUtils.getMilliseconds(filter.getUpdatedOnTo()) > 0;
            if (hasUpdatedOnFrom && hasUpdatedOnTo) {
                predicate = builder.and(predicate, builder.between(root.get("updatedOn"), filter.getUpdatedOnFrom(), filter.getUpdatedOnTo()));
            } else if (hasUpdatedOnFrom) {
                predicate = builder.and(predicate, builder.greaterThan(root.get("updatedOn"), filter.getUpdatedOnFrom()));
            } else if (hasUpdatedOnTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get("updatedOn"), filter.getUpdatedOnTo()));
            }

            boolean hasPaymentDateFrom = filter.getPaymentDateFrom() != null && DateUtils.getMilliseconds(filter.getPaymentDateFrom()) > 0;
            boolean hasPaymentDateTo = filter.getPaymentDateTo() != null && DateUtils.getMilliseconds(filter.getPaymentDateTo()) > 0;
            if (hasPaymentDateFrom && hasPaymentDateTo) {
                predicate = builder.and(predicate, builder.between(root.get("paymentDate"), filter.getPaymentDateFrom(), filter.getPaymentDateTo()));
            } else if (hasPaymentDateFrom) {
                predicate = builder.and(predicate, builder.greaterThan(root.get("paymentDate"), filter.getPaymentDateFrom()));
            } else if (hasPaymentDateTo) {
                predicate = builder.and(predicate, builder.lessThanOrEqualTo(root.get("paymentDate"), filter.getPaymentDateTo()));
            }

            if (!LStringUtils.isEmpty(filter.getKeyword())) {
                Predicate orPredicate = builder.disjunction();

                orPredicate = builder.or(orPredicate, builder.equal(root.get("paymentId"), filter.getKeyword()));
                orPredicate = builder.or(orPredicate, builder.equal(root.get("pgwTxnId"), filter.getKeyword()));

                predicate = builder.and(predicate, orPredicate);
            }

            if (Boolean.TRUE.equals(filter.getRequestInvoice())) {
                predicate = builder.and(predicate, builder.isNotNull(root.get("invoiceBuyerInfo")));
            }

            if (filter.getRequestInvoice() != null) {
                predicate = builder.and(predicate, filter.getRequestInvoice() ? builder.isNotNull(root.get("invoiceBuyerInfo")) : builder.isNull(root.get("invoiceBuyerInfo")));
            }

            if (filter.getInvoiceType() != null) {
                predicate = builder.and(predicate, builder.equal(builder.function("jsonb_extract_path_text", String.class,
                        root.get("invoiceBuyerInfo"), builder.literal("type")), filter.getInvoiceType().getCode()));
            }

            return predicate;
        };
    }

    @Override
    public void updatePaymentInfo(String paymentId, PayGatePaymentResult transactionDetail, String action) throws Exception {
        try (Profiler p = new Profiler(getClass(), "updatePaymentInfo")) {

            try (var l = new VClubLock(redissonClient.getLock(String.format("%s_%s_%s", appName, REDIS_LOCK.TRANSACTION_HISTORY_CUD, paymentId)))) {

                var paymentHistory = optPaymentHistoryByPaymentId(paymentId)
                        .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_HISTORY_NOT_FOUND)));

                if (!paymentHistory.getPaymentId().equals(transactionDetail.getPaymentId())) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_HISTORY_ID_NOT_MATCH));
                }

                if (!transactionDetail.getStatus().equals(paymentHistory.getStatus())) {

                    PaymentHistory oldPaymentHistory = SerializationUtils.clone(paymentHistory);

                    paymentHistory.setStatus(transactionDetail.getStatus());
                    ObjectNode pgwTxnResponse = Objects.nonNull(paymentHistory.getPgwTxnResponse()) ? paymentHistory.getPgwTxnResponse() : JsonNodeFactory.instance.objectNode();
                    pgwTxnResponse.put(action, JsonUtils.toString(transactionDetail));

                    if (PaymentStatusEnum.triggerPointChange(oldPaymentHistory.getStatus(), paymentHistory.getStatus())) {
                        String outboxId = Generators.timeBasedEpochGenerator().generate().toString();

                        var event = PaymentStatusChangedEvent.builder()
                                .oldStatus(oldPaymentHistory.getStatus())
                                .status(paymentHistory.getStatus())
                                .timestamp(Optional.ofNullable(paymentHistory.getUpdatedTime()).orElse(System.currentTimeMillis()))
                                .paymentHistory(paymentHistory)
                                .build();

                        paymentHistory = saveWithOutbox(paymentHistory, outboxId, event);
                        outboxEventService.makeSent(outboxId, null);
                    } else {
                        paymentHistory = _paymentHistoryRepository.save(paymentHistory);
                    }

                    _logHistoricalService.logHistoricalEvent(oldPaymentHistory, paymentHistory);
                } else {
                    log.warn("Payment status not changed, skip update {} => {}", JsonUtils.toString(transactionDetail), JsonUtils.toString(paymentHistory));
                }
            }
        }
    }


    private PaymentHistory initDraftPaymentHistory(Long customerId, CheckoutPaymentRequest checkoutPaymentRequest,
                                                   PaymentMethod paymentMethod, String originalPaymentId, InvoiceBuyerInfo invoiceBuyerInfo) {

        try (var p = new Profiler(getClass(), "initDraftPaymentHistory")) {

            CheckoutPaymentDataRequest checkoutPaymentDataRequest = CheckoutPaymentDataRequest.fromCheckoutRequest(checkoutPaymentRequest);
            checkoutPaymentDataRequest.setFeedFixed(paymentMethod.getFixedFee());
            checkoutPaymentDataRequest.setFeePercent(paymentMethod.getFeePercentage());

            return PaymentHistory.builder()
                    .customerId(customerId)
                    .currency(checkoutPaymentRequest.getCurrency())
                    .transactionType(checkoutPaymentRequest.getTransactionType())

                    .amount(checkoutPaymentRequest.getAmount())
                    .feeAmount(checkoutPaymentRequest.getFeeAmount())
                    .feeDetails(checkoutPaymentDataRequest.getFeeDetails())
                    .discountAmount(checkoutPaymentRequest.getDiscountAmount())
                    .totalAmount(checkoutPaymentRequest.getTotalAmount())

                    .paymentId(genPaymentId())
                    .originalPaymentId(originalPaymentId)
                    .pgwId(paymentMethod.getPgwId())
                    .pgwType(paymentMethod.getPgwType())
                    .serviceProviderId(paymentMethod.getServiceProviderId())
                    .serviceProviderType(paymentMethod.getServiceProviderType())
                    .paymentMethodId(paymentMethod.getId())
                    .paymentMethodType(paymentMethod.getPaymentMethodType())

                    .dataRequest((ObjectNode) JsonUtils.toNode(checkoutPaymentDataRequest))

                    .invoiceBuyerInfo(invoiceBuyerInfo)

                    .paymentDate(LocalDateTime.now())
                    .status(PaymentStatusEnum.PROCESSING)
                    .active(true)
                    .build();
        }
    }

    private PaymentHistory saveWithOutbox(PaymentHistory paymentHistory, String outboxId, PaymentStatusChangedEvent event) {
        try (var p = new Profiler(getClass(), "saveWithOutbox")) {

            TransactionStatus tx = transactionManager.getTransaction(new DefaultTransactionDefinition());
            try {
                PaymentHistory saved = _paymentHistoryRepository.save(paymentHistory);
                outboxEventService.create(event, outboxId);
                transactionManager.commit(tx);
                return saved;
            } catch (Exception e) {
                transactionManager.rollback(tx);
                throw new RuntimeException(e);
            }
        }

    }

    private String genPaymentId() {
        return paymentIdGenerator.nextIdAsString();
    }

}
