package vn.vinclub.payment.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.uuid.Generators;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.LStringUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.InvoiceFilter;
import vn.vinclub.payment.dto.VClubLock;
import vn.vinclub.payment.dto.event.outbox.IssueInvoiceEvent;
import vn.vinclub.payment.dto.internal.*;
import vn.vinclub.payment.dto.invoice.*;
import vn.vinclub.payment.enums.*;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.CustomerInvoiceBuyerInfo;
import vn.vinclub.payment.model.Invoice;
import vn.vinclub.payment.model.InvoiceSerial;
import vn.vinclub.payment.repository.CustomerInvoiceBuyerInfoRepository;
import vn.vinclub.payment.repository.InvoiceRepository;
import vn.vinclub.payment.repository.InvoiceSerialRepository;
import vn.vinclub.payment.repository.spec.InvoiceSpecification;
import vn.vinclub.payment.service.InternalService;
import vn.vinclub.payment.service.InvoiceProviderFactory;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.service.OutboxEventService;
import vn.vinclub.payment.util.ServiceResponse;

import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    public static final Pattern invoicePhoneRegex = Pattern.compile("^\\d{1,20}$");
    public static final Pattern citizenIdRegex = Pattern.compile("^\\d{1,20}$");

    private static final InvoiceBuyerInfoConfigDto.InvoiceConfig bussinessInvoiceConfig = InvoiceBuyerInfoConfigDto.InvoiceConfig.builder()
            .type(InvoiceBuyerTypeEnum.BUSINESS)
            .fields(new HashMap<String, InvoiceBuyerInfoConfigDto.FieldConfig>() {{
                put("name", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("tax_code", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("email", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("address", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
            }})
            .build();

    private static final InvoiceBuyerInfoConfigDto.InvoiceConfig personalInvoiceConfig = InvoiceBuyerInfoConfigDto.InvoiceConfig.builder()
            .type(InvoiceBuyerTypeEnum.PERSONAL)
            .fields(new HashMap<String, InvoiceBuyerInfoConfigDto.FieldConfig>() {{
                put("name", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("citizen_id", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("email", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
                put("address", InvoiceBuyerInfoConfigDto.FieldConfig.builder()
                        .required(true)
                        .editable(true)
                        .build());
            }})
            .build();

    private final InternalService internalService;
    private final OutboxEventService outboxEventService;

    private final CustomerInvoiceBuyerInfoRepository customerInvoiceBuyerInfoRepository;
    private final InvoiceRepository invoiceRepository;

    private final InvoiceProviderFactory invoiceProviderFactory;
    private final InvoiceSerialRepository invoiceSerialRepository;
    private final RedissonClient redissonClient;
    private final PlatformTransactionManager platformTransactionManager;

    @Value("${vinclub.invoice.max-recent-invoice-buyer-infos:5}")
    private Integer maxRecentInvoiceBuyerInfos = 5;

    @Override
    public List<InvoiceBuyerInfo> getRecentInvoiceBuyerInfos(Long customerId) {
        CustomerInvoiceBuyerInfo invoiceBuyerInfo = customerInvoiceBuyerInfoRepository.findByCustomerId(customerId)
                .orElse(null);
        if (invoiceBuyerInfo == null || CollectionUtils.isEmpty(invoiceBuyerInfo.getRecentInvoiceBuyerInfos())) {
            return Collections.emptyList();
        }
        return invoiceBuyerInfo.getRecentInvoiceBuyerInfos();
    }

    @Override
    public InvoiceBuyerInfoConfigDto getInvoiceBuyerConfigs(Long customerId) {
        CoreCustomerDto customer = internalService.optCustomer(CoreCustomerIdentifyRequest.builder()
                .vclubUserId(customerId)
                        .build())
                .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.CUSTOMER_NOT_FOUND)));
        InvoiceBuyerInfoConfigDto result = InvoiceBuyerInfoConfigDto.builder().build();

        InvoiceBuyerInfoConfigDto.InvoiceConfig personalInvoiceConfig = SerializationUtils.clone(InvoiceServiceImpl.personalInvoiceConfig);

        if (customer.isIdentityDocumentVerified()) {
            CoreCustomerIdentityDocumentFilterDto filterDto = new CoreCustomerIdentityDocumentFilterDto();
            filterDto.setCustomerId(String.valueOf(customerId));
            filterDto.setApprovalStatus(ApprovalStatus.SUCCESS);

            CoreCustomerIdentityVerificationDto identityVerification = internalService.findCustomerIdentityVerificationByFilter(filterDto, Pageable.ofSize(1))
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (identityVerification != null) {
                personalInvoiceConfig.getFields().get("name").setEditable(false);
                personalInvoiceConfig.getFields().get("name").setValue(identityVerification.getCustomerIdentityInfo().getFullName());

                personalInvoiceConfig.getFields().get("citizen_id").setEditable(false);
                personalInvoiceConfig.getFields().get("citizen_id").setValue(identityVerification.getCustomerIdentityInfo().getNo());
            }
        }

        result.setConfigs(Arrays.asList(personalInvoiceConfig, bussinessInvoiceConfig));
        return result;
    }

    @Override
    public InvoiceBuyerInfo validateInvoiceBuyerInfo(Long customerId, InvoiceBuyerInfo invoiceBuyerInfo) {
        if (invoiceBuyerInfo.getType() == null) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PARAM_REQUIRED, "type"));
        }

        InvoiceBuyerInfoConfigDto.InvoiceConfig config = getInvoiceBuyerConfigs(customerId).getConfigs().stream().filter(e -> e.getType().equals(invoiceBuyerInfo.getType()))
                .findFirst().orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "invoice")));

        if (invoiceBuyerInfo.getName() != null && invoiceBuyerInfo.getName().length() > 256) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "name"));
        }
        validateInvoiceBuyerField(config.getFields().get("name"), invoiceBuyerInfo.getName(), "name");

        if (invoiceBuyerInfo.getPhone() != null && !invoicePhoneRegex.matcher(invoiceBuyerInfo.getPhone()).matches()) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PARAM_REQUIRED, "phone"));
        }
        validateInvoiceBuyerField(config.getFields().get("phone"), invoiceBuyerInfo.getPhone(), "phone");

        if (!LStringUtils.isEmail(invoiceBuyerInfo.getEmail())) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "email"));
        }
        validateInvoiceBuyerField(config.getFields().get("email"), invoiceBuyerInfo.getEmail(), "email");

        if (invoiceBuyerInfo.getAddress() != null && invoiceBuyerInfo.getAddress().length() > 256) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "address"));
        }
        validateInvoiceBuyerField(config.getFields().get("address"), invoiceBuyerInfo.getAddress(), "address");

        if (invoiceBuyerInfo.getTaxCode() != null && invoiceBuyerInfo.getTaxCode().length() > 20) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "tax_code"));
        }
        validateInvoiceBuyerField(config.getFields().get("tax_code"), invoiceBuyerInfo.getTaxCode(), "tax_code");

        if (invoiceBuyerInfo.getCitizenId() != null && !citizenIdRegex.matcher(invoiceBuyerInfo.getCitizenId()).matches()) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "citizen_id"));
        }
        validateInvoiceBuyerField(config.getFields().get("citizen_id"), invoiceBuyerInfo.getCitizenId(), "citizen_id");

        return invoiceBuyerInfo;
    }

    private void validateInvoiceBuyerField(InvoiceBuyerInfoConfigDto.FieldConfig fieldConfig, String value, String name) {
        if (fieldConfig == null) {
            return;
        }
        if (fieldConfig.isRequired() && value == null) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PARAM_REQUIRED, name));
        }
        if (!fieldConfig.isEditable() && !Objects.equals(value, fieldConfig.getValue())) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, name));
        }
    }

    @Override
    public void handleInvoiceRequestEvent(InvoiceRequestEvent event) {
        if (event == null || event.getCustomerId() == null || event.getInvoiceBuyerInfo() == null) {
            log.warn("Received empty invoice request event: {}", event);
            return;
        }

        InvoiceBuyerInfo newInvoiceBuyerInfo = validateInvoiceBuyerInfo(event.getCustomerId(), event.getInvoiceBuyerInfo());
        newInvoiceBuyerInfo.setLastUsedTime(event.getTimestamp());

        CustomerInvoiceBuyerInfo customerInvoiceBuyerInfo = customerInvoiceBuyerInfoRepository.findByCustomerId(event.getCustomerId())
                .orElse(new CustomerInvoiceBuyerInfo(event.getCustomerId(), new ArrayList<>()));

        // store 5 newest invoice infos by type
        int minUsedTimeIndex = -1;
        long minUsedTimeValue = Long.MAX_VALUE;
        int countByType = 0;
        for (InvoiceBuyerInfo invoiceBuyerInfo : customerInvoiceBuyerInfo.getRecentInvoiceBuyerInfos()) {
            if (invoiceBuyerInfo.isSame(newInvoiceBuyerInfo)) {
                if (invoiceBuyerInfo.getLastUsedTime() < newInvoiceBuyerInfo.getLastUsedTime()) {
                    invoiceBuyerInfo.setLastUsedTime(newInvoiceBuyerInfo.getLastUsedTime());
                    customerInvoiceBuyerInfoRepository.save(customerInvoiceBuyerInfo);
                }
                return;
            }
            if (Objects.equals(invoiceBuyerInfo.getType(), newInvoiceBuyerInfo.getType())) {
                countByType++;
                if (invoiceBuyerInfo.getLastUsedTime() < minUsedTimeValue) {
                    minUsedTimeValue = invoiceBuyerInfo.getLastUsedTime();
                    minUsedTimeIndex = customerInvoiceBuyerInfo.getRecentInvoiceBuyerInfos().indexOf(invoiceBuyerInfo);
                }
            }
        }
        if (countByType >= maxRecentInvoiceBuyerInfos) {
            customerInvoiceBuyerInfo.getRecentInvoiceBuyerInfos().set(minUsedTimeIndex, newInvoiceBuyerInfo);
        } else {
            customerInvoiceBuyerInfo.getRecentInvoiceBuyerInfos().add(newInvoiceBuyerInfo);
        }
        customerInvoiceBuyerInfoRepository.save(customerInvoiceBuyerInfo);
    }

    @Override
    public Invoice createDraftInvoice(CreateInvoiceRequest request) {
        getInvoiceByFilter(InvoiceFilter.builder()
                .transactionType(request.getTransactionType())
                .transactionId(request.getTransactionId())
                .build()).ifPresent((i) -> {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_ALREADY_EXITS, request.getTransactionId()));
        });

        Invoice invoice = new Invoice();
        invoice.setTransactionType(request.getTransactionType());
        invoice.setTransactionId(request.getTransactionId());
        invoice.setCustomerId(request.getCustomerId());
        invoice.setSource(request.getSource());
        invoice.setStatus(InvoiceIssueStatusEnum.DRAFT);
        invoice.setIssueType(InvoiceIssueTypeEnum.ORIGINAL);
        invoice.setSapPostingStatus(SapPostingStatusEnum.NOT_READY);
        invoice.setActive(true);
        invoice.setBuyerInfo(request.getBuyerInfo());
        invoice.setItems(request.getItems());
        invoice.setCurrency(request.getCurrency());


        BigDecimal amount = BigDecimal.valueOf(0);
        BigDecimal vatAmount = BigDecimal.valueOf(0);
        BigDecimal totalAmount = BigDecimal.valueOf(0);
        for (Invoice.InvoiceProduct item : request.getItems()) {
            amount = amount.add(item.getAmount());
            vatAmount = vatAmount.add(item.getVatAmount());
            totalAmount = totalAmount.add(item.getTotalAmount());
        }
        invoice.setAmount(amount);
        invoice.setVatAmount(vatAmount);
        invoice.setTotalAmount(totalAmount);

        var savedInvoice = saveInvoice(invoice);

        // process issue invoice for auto source
        if (InvoiceSourceEnum.AUTO.equals(request.getSource())) {
            CompletableFuture.runAsync(() -> processIssueInvoice(savedInvoice.getId()));
        }

        return savedInvoice;
    }

    @Override
    public void processIssueInvoice(Long invoiceId) {
        try (var p = new Profiler(getClass(), "processIssueInvoice")) {
            try (var l = new VClubLock(redissonClient, AppConst.REDIS_LOCK.INVOICE_CUD + invoiceId)) {

                Invoice invoice = invoiceRepository.findById(invoiceId)
                        .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, "Invoice", "id", invoiceId)));

                if (!Objects.equals(invoice.getStatus(), InvoiceIssueStatusEnum.DRAFT)
                        && !Objects.equals(invoice.getStatus(), InvoiceIssueStatusEnum.FAILED)) {
                    log.error("[processIssueInvoice] Invoice {} is not in DRAFT/FAILED status, cannot issue", invoiceId);
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_INVALID_STATUS_FOR_ISSUE));
                }

                TransactionStatus transaction = null;
                try {
                    transaction = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());

                    // if draft, get invoice serial
                    if (InvoiceIssueStatusEnum.DRAFT.equals(invoice.getStatus())) {
                        // get invoice serial
                        InvoiceSerial invoiceSerial = invoiceSerialRepository.findValidSerial(invoice.getSource().getCode())
                                .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, "Invoice serial")));
                        var newInvoiceNo = invoiceSerial.getCurrentInvoiceNo() + 1;

                        // update invoice serial
                        invoiceSerial.setCurrentInvoiceNo(newInvoiceNo);
                        invoiceSerialRepository.save(invoiceSerial);

                        // mark invoice serial info
                        invoice.setProvider(invoiceSerial.getProvider());
                        invoice.setBrandId(invoiceSerial.getBrandId());
                        invoice.setPatternNo(invoiceSerial.getPatternNo());
                        invoice.setSerialNo(invoiceSerial.getSerialNo());
                        invoice.setInvoiceNo(newInvoiceNo);
                        invoice.setProviderUniqueId(invoiceProviderFactory.getProvider(invoiceSerial.getProvider()).getProviderUniqueId(invoice));
                    }

                    // mark invoice as processing
                    invoice.setStatus(InvoiceIssueStatusEnum.PROCESSING);

                    // calculate retry count
                    var retryCount = Optional.ofNullable(invoice.getMetadata()).map(m -> m.get("issue_retry_count").asInt(-1)).orElse(-1) + 1;
                    invoice.putMetadata("issue_retry_count", retryCount);
                    saveInvoice(invoice);

                    // create outbox issueEvent for auto source
                    if (InvoiceSourceEnum.AUTO.equals(invoice.getSource())) {
                        var outboxId = Generators.timeBasedEpochGenerator().generate().toString();
                        var issueEvent = IssueInvoiceEvent.builder()
                                .invoiceId(invoice.getId())
                                .transactionType(invoice.getTransactionType())
                                .transactionId(invoice.getTransactionId())
                                .source(invoice.getSource())
                                .provider(invoice.getProvider())
                                .brandId(invoice.getBrandId())
                                .patternNo(invoice.getPatternNo())
                                .serialNo(invoice.getSerialNo())
                                .invoiceNo(invoice.getInvoiceNo())
                                .timestamp(System.currentTimeMillis())
                                .build();
                        outboxEventService.create(issueEvent, outboxId);
                        platformTransactionManager.commit(transaction);

                        // send issueEvent to kafka after the commit transaction
                        try {
                            outboxEventService.makeSent(outboxId, null);
                        } catch (Exception e) {
                            log.error("[processIssueInvoice] Error when sending issueEvent to kafka: {}", e.getMessage(), e);
                        }
                    } else { // issue invoice directly for a manual source
                        platformTransactionManager.commit(transaction);
                        issueInvoice(invoice);
                    }
                } catch (Exception e) {
                    log.error("[processIssueInvoice] Error when issuing invoice {}: {}", invoiceId, e.getMessage(), e);
                    throw e;
                } finally {
                    if (transaction != null && !transaction.isCompleted()) {
                        log.error("[processIssueInvoice] Transaction is not completed, rollback it");
                        platformTransactionManager.rollback(transaction);
                    }
                }
            }
        }
    }

    @Override
    public void issueInvoiceByEvent(IssueInvoiceEvent event) {
        Long invoiceId = event.getInvoiceId();
        try (var p = new Profiler(getClass(), "issueInvoiceByEvent"); var l = new VClubLock(redissonClient, AppConst.REDIS_LOCK.INVOICE_CUD + event.getInvoiceId())) {
            Invoice invoice = invoiceRepository.findById(invoiceId).orElse(null);

            if (invoice == null) {
                log.error("[issueInvoiceByEvent] Invoice not found, skip issue");
                return;
            }

            // validate event data
            if (!event.isSameWith(invoice)) {
                log.error("[issueInvoiceByEvent] Event data is not same with invoice data, skip issue");
                return;
            }

            if (!Objects.equals(invoice.getStatus(), InvoiceIssueStatusEnum.PROCESSING)) {
                log.warn("[issueInvoiceByEvent] Invoice {} is not in PROCESSING status. Current status: {}, skip issue", invoiceId, invoice.getStatus());
                return;
            }
            // issue invoice
            issueInvoice(invoice);
        }
    }

    private void issueInvoice(Invoice invoice) {
        try (var p = new Profiler(getClass(), "issueInvoice"); var l = new VClubLock(redissonClient, AppConst.REDIS_LOCK.INVOICE_ISSUING + invoice.getSource().name())) {
            try {
                IssueInvoiceResult issueInvoiceResult = invoiceProviderFactory.getProvider(invoice.getProvider()).issueInvoice(invoice);
                if (StringUtils.isAllBlank(invoice.getProviderRequest(), invoice.getProviderResponse())) {
                    invoice.setProviderRequest(issueInvoiceResult.getRequest());
                    invoice.setProviderResponse(issueInvoiceResult.getResponse());
                } else {
                    invoice.putMetadata("last_retry_request", issueInvoiceResult.getRequest());
                    invoice.putMetadata("last_retry_response", issueInvoiceResult.getResponse());
                }

                if (issueInvoiceResult.getException() != null) {
                    throw issueInvoiceResult.getException();
                } else {
                    invoice.setStatus(InvoiceIssueStatusEnum.ISSUED);
                    invoice.setSapPostingStatus(SapPostingStatusEnum.WAITING);
                    invoice.setIssuedAt(LocalDateTime.now());
                    saveInvoice(invoice);
                }
            } catch (Exception e) {
                log.error("[issueInvoice] Error when issuing invoice {}: {}", invoice.getId(), e.getMessage(), e);
                invoice.setStatus(InvoiceIssueStatusEnum.FAILED);
                invoice.putMetadata("issue_error_message", e.getMessage());
                saveInvoice(invoice);
                throw e;
            }
        }
    }

    private Invoice saveInvoice(Invoice invoice) {
        return invoiceRepository.save(invoice);
    }

    @Override
    public Page<Invoice> getAllInvoicesByFilter(InvoiceFilter filter, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getAllInvoicesByFilter")) {
            Pageable _pageable = pageable;
            _pageable.getSort();
            if (_pageable.getSort().isUnsorted()) {
                _pageable = PageRequest.of(_pageable.getPageNumber(), _pageable.getPageSize(),
                        Sort.by(Sort.Order.desc("id"))
                );
            }

            Specification<Invoice> specification = InvoiceSpecification.buildFilter(filter);

            return invoiceRepository.findAll(specification, _pageable);
        }
    }

    public Optional<Invoice> getInvoiceByFilter(InvoiceFilter filter) {
        try (var p = new Profiler(getClass(), "getInvoiceByFilter")) {
            Specification<Invoice> specification = InvoiceSpecification.buildFilter(filter);

            return invoiceRepository.findOne(specification);
        }
    }

    @Override
    public void scanAutoInvoiceToIssue() {
        try (var p = new Profiler(getClass(), "scanAutoInvoiceToIssue")) {
            int pageSize = 100;
            int processed = 0;
            while (true) {
                InvoiceFilter filter = InvoiceFilter.builder()
                        .statuses(Set.of(InvoiceIssueStatusEnum.DRAFT, InvoiceIssueStatusEnum.FAILED))
                        .source(InvoiceSourceEnum.AUTO)
                        .build();
                Page<Invoice> invoices = getAllInvoicesByFilter(filter, PageRequest.of(0, pageSize));
                for (Invoice invoice : invoices) {
                    processIssueInvoice(invoice.getId());
                }
                processed += invoices.getNumberOfElements();
                if (invoices.getNumberOfElements() < pageSize) {
                    break;
                }
                log.info("Processed {} draft/failed invoices, continue to next page...", processed);
            }
            log.info("Finished issue {} draft/failed invoices...", processed);
        }
    }

    @Override
    public URL downloadInvoice(Long invoiceId) {
        try (var p = new Profiler(getClass(), "downloadInvoice")) {
            Invoice invoice = invoiceRepository.findById(invoiceId)
                    .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.NOT_FOUND, "Invoice", "id", invoiceId)));

            if (!Objects.equals(invoice.getStatus(), InvoiceIssueStatusEnum.ISSUED)) {
                log.error("[getInvoicePdf] Invoice {} is not in ISSUED status, cannot get PDF", invoiceId);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_INVALID_STATUS_FOR_DOWNLOAD));
            }

            DownloadInvoiceResponse downloadInvoiceResponse = invoiceProviderFactory.getProvider(invoice.getProvider()).downloadInvoice(invoice);
            byte[] fileContentBytes = Base64.getDecoder().decode(downloadInvoiceResponse.getBase64Content());
            Map<String, URL> signedUrl = internalService.getSignedUrlInternalPath(String.format("%s.pdf", invoice.getProviderUniqueId()), true);

            internalService.uploadFileByPresignedUrl(signedUrl.get("PUT"), fileContentBytes);
            return signedUrl.get("GET");
        }
    }
}
