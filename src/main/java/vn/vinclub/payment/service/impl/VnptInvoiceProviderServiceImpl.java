package vn.vinclub.payment.service.impl;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.invoice.CheckInvoiceResult;
import vn.vinclub.payment.dto.invoice.DownloadInvoiceResponse;
import vn.vinclub.payment.dto.invoice.IssueInvoiceResult;
import vn.vinclub.payment.enums.InvoiceBuyerTypeEnum;
import vn.vinclub.payment.enums.InvoiceIssueTypeEnum;
import vn.vinclub.payment.enums.InvoiceProviderEnum;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.Invoice;
import vn.vinclub.payment.service.InvoiceProviderService;
import vn.vinclub.payment.util.Base64Util;
import vn.vinclub.payment.util.NumberUtils;
import vn.vinclub.payment.util.ServiceResponse;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class VnptInvoiceProviderServiceImpl implements InvoiceProviderService {
    private static final String CUSTOMER_NO_REQUEST_INVOICE_NO = "KHÁCH HÀNG KHÔNG LẤY HÓA ĐƠN";
    private static final String ERROR_RESULT_CODE = "ERR";
    private static final String PARAM_OPERATOR = "op";

    private static final String SERVICE_DOMAIN_PUBLISH = "PublishServiceOneDomain.asmx";
    private static final String SERVICE_DOMAIN_PORTAL = "PortalServiceOneDomain.asmx";

    // Tạo mới và phát hành hóa đơn cùng lúc
    private static final String OP_IMPORT_AND_PUBLISH = "ImportAndPublishAssignedNo";

    // Tạo mới hóa đơn
    private static final String OP_IMPORT = "ImportInvByPattern";

    // Phát hành hóa đơn
    private static final String OP_PUBLISH = "PublishInvFkey";

    // Điều chỉnh hóa đơn
    private static final String OP_ADJUST = "AdjustActionAssignedNo";

    // Điều chỉnh hóa đơn khác mẫu số
    private static final String OP_ADJUST_NEW_PATTERN = "AdjustAssignedNoNewPattern";

    // Thay thế hóa đơn
    private static final String OP_REPLACE = "ReplaceActionAssignedNo";

    // Kiểm tra trạng thái hóa đơn
    private static final String OP_CHECK = "checkFkey";

    // Tải về file PDF
    private static final String OP_DOWNLOAD_PDF = "downloadInvPDFFkeyByStaff";

    // Tải về file XML
    private static final String OP_DOWNLOAD_XML = "downloadInvFkeyByStaff";

    private static final XmlMapper xmlMapper;

    static {
        xmlMapper = new XmlMapper();
        xmlMapper.registerModule(new JavaTimeModule());
        xmlMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Value("${invoice_provider.vnpt.url}")
    private String vnptInvoiceProviderUrl;

    @Value("${invoice_provider.vnpt.account}")
    private String vnptAccount;

    @Value("${invoice_provider.vnpt.ac_pass}")
    private String vnptAcPass;

    @Value("${invoice_provider.vnpt.username}")
    private String vnptUsername;

    @Value("${invoice_provider.vnpt.password}")
    private String vnptPassword;

    @Value("${vinclub.invoice.company-alias}")
    private String companyAlias;

    @Autowired
    @Qualifier("proxyRestTemplate")
    private RestTemplate restTemplate;

    private HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "text/xml; charset=utf-8");

        return headers;
    }

    @Override
    public InvoiceProviderEnum getProvider() {
        return InvoiceProviderEnum.VNPT;
    }

    @Override
    public String getProviderUniqueId(Invoice invoice) {
        return buildFKey(invoice.getBrandId(), invoice.getPatternNo(), invoice.getSerialNo(), invoice.getInvoiceNo());
    }

    @Override
    public IssueInvoiceResult issueInvoice(Invoice invoice) {
        String requestData = null;
        String responseData = null;
        try (Profiler p = new Profiler(getClass(), "issueInvoice")) {
            log.info("[issueInvoice] Current invoice: {}", JsonUtils.toString(invoice));

            ImportAndPublishAssignedNoRequest importInvByPatternRequest = buildImportAndPublishAssignedNoRequest(invoice, invoice.getInvoiceNo());

            requestData = buildRequest(importInvByPatternRequest);

            HttpHeaders headers = getDefaultHeaders();

            HttpEntity<String> request = new HttpEntity<>(requestData, headers);
            String url = UriComponentsBuilder.fromHttpUrl(vnptInvoiceProviderUrl)
                    .path(SERVICE_DOMAIN_PUBLISH)
                    .queryParam(PARAM_OPERATOR, OP_IMPORT_AND_PUBLISH)
                    .toUriString();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

            responseData = response.toString();
            JsonNode jsonBody = parseResponse(response.getBody(), JsonNode.class);
            String noResult = jsonBody.at("/ImportAndPublishAssignedNoResult").asText();

            log.info("Create Invoice {} with result {}", JsonUtils.toString(jsonBody), noResult);
            if (noResult == null || noResult.isEmpty()) {
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
            }

            // Result format: CODE:data
            String[] parts = noResult.split(":");
            if (parts.length < 2) {
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
            }
            String resultCode = parts[0];
            String resultData = parts[1];

            if (ERROR_RESULT_CODE.equals(resultCode)) {
                // error data format: CODE [DATA]
                String[] errorParts = resultData.split(" ");
                if (errorParts.length < 1) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
                }
                String errorCode = errorParts[0];
                //ERR:13 => Lỗi trùng fkey
                //ERR:21 => Lỗi trùng số hóa đơn
                if ("13".equals(errorCode) || "21".equals(errorCode)) {
                    // Nếu gọi lần đầu mà đã tồn tại thì chứng tỏ có lỗi khi gán số cho hóa đơn => không cần gọi kiểm tra mà quăng lỗi luôn
                    var retryCount = Optional.ofNullable(invoice.getMetadata()).map(m -> m.get("issue_retry_count").asInt(0)).orElse(0);
                    if (retryCount == 0) {
                        if ("13".equals(errorCode)) {
                            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_DUPLICATE_UNIQUE_ID));
                        }
                        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_DUPLICATE_INVOICE_NO));
                    }

                    // Nếu đã thử lại => Gọi hàm kiểm tra trạng thái hóa đơn
                    var checkResult = checkInvoice(invoice);
                    if (checkResult.getException() == null) {
                        if (!checkResult.isPublished()) {
                            // Nếu chưa phát hành => Phát hành lại
                            return publishInvoice(invoice);
                        }
                        return IssueInvoiceResult.builder()
                                .invoiceId(invoice.getId())
                                .patternNo(invoice.getPatternNo())
                                .serialNo(invoice.getSerialNo())
                                .invoiceNo(invoice.getInvoiceNo())
                                .providerUniqueId(invoice.getProviderUniqueId())
                                .request(checkResult.getRequest())
                                .response(checkResult.getResponse())
                                .build();
                    } else {
                        throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_CHECK_ERROR, checkResult.getException().getMessage()));
                    }
                }

                // ERR:3 => Dữ liệu xml đầu vào không đúng quy định
                if ("3".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_REQUEST));
                }

                // ERR:20 => Pattern và Serial không phù hợp, hoặc không tồn tại hóa đơn đã đăng kí có sử dụng Pattern và Serial truyền vào.
                if ("20".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_SERIAL_NOT_FOUND));
                }

                // ERR:6 => Dải hóa đơn không đủ số hóa đơn cho lô phát hành
                if ("6".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_SERIAL_NOT_ENOUGH));
                }

                // ERR:29 => Lỗi chứng thư hết hạn
                if ("29".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_CERTIFICATE_EXPIRED));
                }

                // ERR:30 => Danh sách hóa đơn tồn tại ngày hóa đơn nhỏ hơn ngày hóa đơn đã phát hành
                if ("30".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_DATE_INVALID));
                }

                // ERR:31 X =>Số hóa đơn truyền vào không hợp lệ, số hóa đơn hợp lệ phải là X
                if ("31".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_NO_INVALID, errorParts[1]));
                }

                // ERR:1 => Tài khoản đăng nhập sai hoặc không có quyền thêm khách hàng
                // ERR:7 => Thông tin về Username/pass không hợp lệ
                if ("1".equals(errorCode) || "7".equals(errorCode)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_CONFIG, resultData));
                }

                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, resultData));
            }

            String[] result = parts[1].split(";");
            String pattern = result[0];
            String serial = result[1].split("-")[0];
            String fkey = result[1].split("-")[1].split("_")[0];
            Long invoiceNo = Long.valueOf(result[1].split("-")[1].split("_")[1]);

            return IssueInvoiceResult.builder()
                    .invoiceId(invoice.getId())
                    .patternNo(pattern)
                    .serialNo(serial)
                    .invoiceNo(invoiceNo)
                    .providerUniqueId(fkey)
                    .request(requestData)
                    .response(responseData)
                    .build();

        } catch (HttpStatusCodeException e) {
            log.error("[issueInvoice] HttpStatusCodeException when issue Invoice", e);
            responseData = e.getResponseBodyAsString();
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, "HttpCode=" + e.getStatusCode())))
                    .build();
        } catch (BusinessLogicException e) {
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(e)
                    .build();
        } catch (Exception e) {
            log.error("[issueInvoice] Exception when issue Invoice", e);
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR)))
                    .build();
        }
    }

    @Override
    public CheckInvoiceResult checkInvoice(Invoice invoice) {
        String requestData = null;
        String responseData = null;
        try (var p = new Profiler(getClass(), "checkInvoice")) {
            CheckFkey checkFkeyRequest = CheckFkey.builder()
                    .fKey(invoice.getProviderUniqueId())
                    .username(vnptUsername)
                    .pass(vnptPassword)
                    .build();

            String soapRequest = buildRequest(checkFkeyRequest);
            requestData = soapRequest;

            HttpHeaders headers = getDefaultHeaders();

            HttpEntity<String> request = new HttpEntity<>(soapRequest, headers);
            String url = UriComponentsBuilder.fromHttpUrl(vnptInvoiceProviderUrl)
                    .path(SERVICE_DOMAIN_PORTAL)
                    .queryParam(PARAM_OPERATOR, OP_CHECK)
                    .toUriString();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            responseData = response.toString();

            JsonNode jsonBody = parseResponse(response.getBody(), JsonNode.class);
            String result = jsonBody.at("/checkFkeyResult").asText();

            if (result == null || result.isEmpty()) {
                log.error("[checkInvoice] Error when check invoice: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
            }

            if (result.startsWith(ERROR_RESULT_CODE)) {
                // ERR:6 => Không tìm thấy hóa đơn hoặc hóa đơn chưa được phát hành
                switch (result) {
                    case "ERR:6" -> {
                        return CheckInvoiceResult.builder()
                                .invoiceId(invoice.getId())
                                .isPublished(false)
                                .request(requestData)
                                .response(responseData)
                                .build();
                    }

                    // ERR:1 => Tài khoản đăng nhập sai
                    // ERR:7 => Thông tin về Username/pass không hợp lệ
                    case "ERR:1", "ERR:7" ->
                            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_CONFIG, result));

                    // ERR:4 => Công ty chưa được đăng kí mẫu hóa đơn nào
                    case "ERR:4" ->
                            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_SERIAL_NOT_FOUND, result));
                }

                log.error("[checkInvoice] Error when check invoice: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, result));
            }

            // N => N là trạng thái hóa đơn: 1- hóa đơn đã phát hành, 3- hóa đơn bị thay thế, 4-hóa đơn bị điều chỉnh,  5-hóa đơn bị hủy
            InvoiceIssueTypeEnum invoiceIssueType = switch (result) {
                case "1" -> InvoiceIssueTypeEnum.ORIGINAL;
                case "3" -> InvoiceIssueTypeEnum.REPLACED;
                case "4" -> InvoiceIssueTypeEnum.ADJUSTED;
                case "5" -> InvoiceIssueTypeEnum.CANCELLED;
                default -> null;
            };

            return CheckInvoiceResult.builder()
                    .invoiceId(invoice.getId())
                    .invoiceIssueType(invoiceIssueType)
                    .isPublished(true)
                    .request(requestData)
                    .response(responseData)
                    .build();
        } catch (HttpStatusCodeException e) {
            log.error("[checkInvoice] HttpStatusCodeException when check invoice", e);
            responseData = e.getResponseBodyAsString();
            return CheckInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, "HttpCode=" + e.getStatusCode())))
                    .build();
        } catch (BusinessLogicException e) {
            return CheckInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(e)
                    .build();
        } catch (Exception e) {
            log.error("[checkInvoice] Exception when check invoice", e);
            return CheckInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR)))
                    .build();
        }
    }

    @Override
    public DownloadInvoiceResponse downloadInvoice(Invoice invoice) {
        try (var p = new Profiler(getClass(), "downloadInvoice")) {
            DownloadInvPDFFkeyByStaff downloadInvRequest = DownloadInvPDFFkeyByStaff.builder()
                    .brandID(invoice.getBrandId())
                    .username(vnptUsername)
                    .pass(vnptPassword)
                    .fKey(invoice.getProviderUniqueId())
                    .build();

            String soapRequest = buildRequest(downloadInvRequest);

            HttpHeaders headers = getDefaultHeaders();

            HttpEntity<String> request = new HttpEntity<>(soapRequest, headers);
            String url = UriComponentsBuilder.fromHttpUrl(vnptInvoiceProviderUrl)
                    .path(SERVICE_DOMAIN_PORTAL)
                    .queryParam(PARAM_OPERATOR, OP_DOWNLOAD_PDF)
                    .toUriString();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

            JsonNode jsonBody = parseResponse(response.getBody(), JsonNode.class);
            String result = jsonBody.at("/downloadInvPDFFkeyByStaffResult").asText();
            if (result == null || result.isEmpty()) {
                log.error("[downloadInvoice] Error when get current invoice number: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
            }
            if (result.startsWith(ERROR_RESULT_CODE)) {
                log.error("[downloadInvoice] Error when get current invoice number: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, result));
            }
            return DownloadInvoiceResponse.builder()
                    .base64Content(result)
                    .build();
        } catch (HttpStatusCodeException e) {
            log.error("[downloadInvoice] HttpStatusCodeException when get current invoice number", e);
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, "HttpCode=" + e.getStatusCode()));
        } catch (BusinessLogicException e) {
            throw e;
        } catch (Exception e) {
            log.error("[getCurrentNo] Exception when get current invoice number", e);
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    private IssueInvoiceResult publishInvoice(Invoice invoice) {
        String requestData = null;
        String responseData = null;
        try (var p = new Profiler(getClass(), "publishInvoice")) {
            PublishInvFkeyRequest publishInvRequest = PublishInvFkeyRequest.builder()
                    .brandID(invoice.getBrandId())
                    .account(vnptAccount)
                    .acPass(vnptAcPass)
                    .username(vnptUsername)
                    .pass(vnptPassword)
                    .pattern(invoice.getPatternNo())
                    .serial(invoice.getSerialNo())
                    .fkeys(Collections.singletonList(invoice.getProviderUniqueId()))
                    .build();

            String soapRequest = buildRequest(publishInvRequest);
            requestData = soapRequest;

            HttpHeaders headers = getDefaultHeaders();

            HttpEntity<String> request = new HttpEntity<>(soapRequest, headers);
            String url = UriComponentsBuilder.fromHttpUrl(vnptInvoiceProviderUrl)
                    .path(SERVICE_DOMAIN_PUBLISH)
                    .queryParam(PARAM_OPERATOR, OP_PUBLISH)
                    .toUriString();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            responseData = response.toString();

            JsonNode jsonBody = parseResponse(response.getBody(), JsonNode.class);
            String result = jsonBody.at("/PublishInvFkeyResult").asText();

            if (result == null || result.isEmpty()) {
                log.error("[publishInvoice] Error when publish invoice: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_RESPONSE));
            }

            if (result.startsWith(ERROR_RESULT_CODE)) {
                // ERR:15 => Danh sách Fkey đã phát hành
                if (result.startsWith("ERR:15")) {
                    return IssueInvoiceResult.builder()
                            .invoiceId(invoice.getId())
                            .patternNo(invoice.getPatternNo())
                            .serialNo(invoice.getSerialNo())
                            .invoiceNo(invoice.getInvoiceNo())
                            .providerUniqueId(invoice.getProviderUniqueId())
                            .request(requestData)
                            .response(responseData)
                            .build();
                }
                // ERR:1 => Tài khoản đăng nhập sai
                if ("ERR:1".equals(result)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_CONFIG, result));
                }

                // ERR:6  => Danh sách Fkey không tồn tại
                if (result.startsWith("ERR:6")) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_INVALID_REQUEST, result));
                }

                // ERR:29 => Lỗi chứng thư hết hạn
                if ("ERR:29".equals(result)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_CERTIFICATE_EXPIRED, result));
                }

                // ERR:30 => Danh sách hóa đơn tồn tại ngày hóa đơn nhỏ hơn ngày hóa đơn đã phát hành
                if ("ERR:30".equals(result)) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_DATE_INVALID, result));
                }
                log.error("[publishInvoice] Error when publish invoice: {}", result);
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, result));
            }

            return IssueInvoiceResult.builder()
                    .invoiceId(invoice.getId())
                    .patternNo(invoice.getPatternNo())
                    .serialNo(invoice.getSerialNo())
                    .invoiceNo(invoice.getInvoiceNo())
                    .providerUniqueId(invoice.getProviderUniqueId())
                    .request(requestData)
                    .response(responseData)
                    .build();
        } catch (HttpStatusCodeException e) {
            log.error("[publishInvoice] HttpStatusCodeException when publish invoice", e);
            responseData = e.getResponseBodyAsString();
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVOICE_PROVIDER_ERROR, "HttpCode=" + e.getStatusCode())))
                    .build();
        } catch (BusinessLogicException e) {
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(e)
                    .build();
        } catch (Exception e) {
            log.error("[publishInvoice] Exception when publish invoice", e);
            return IssueInvoiceResult.builder()
                    .request(requestData)
                    .response(responseData)
                    .exception(new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR)))
                    .build();
        }
    }

    private ImportAndPublishAssignedNoRequest buildImportAndPublishAssignedNoRequest(Invoice invoice, Long currentNo) {
        String fKey = buildFKey(invoice.getBrandId(), invoice.getPatternNo(), invoice.getSerialNo(), currentNo);

        List<VnptProduct> products = invoice.getItems().stream()
                .map(item -> VnptProduct.builder()
                        .prodName(item.getProductName())
                        .prodUnit(item.getProductUnit())
                        .prodQuantity(item.getQuantity())
                        .prodPrice(item.getUnitPrice())
                        .total(item.getAmount())
                        .vatRate(item.getVatRate())
                        .vatAmount(item.getVatAmount())
                        .amount(item.getTotalAmount())
                        .isSum(0)
                        .build())
                .collect(Collectors.toList());

        VnptInvoice vnptInvoice = VnptInvoice.builder()
                .salt(buildSalt(fKey))
                .invoiceNo(String.valueOf(currentNo))
                .products(products)
                .total(invoice.getAmount())
                .vatAmount(invoice.getVatAmount())
                .amount(invoice.getTotalAmount())
                .amountInWords(NumberUtils.convertToWords(invoice.getTotalAmount().longValue(), invoice.getCurrency()))
                .arisingDate(LocalDateTime.now())
                .businessDate(invoice.getCreatedOn())
                .build();

        if (invoice.getBuyerInfo() != null) {
            vnptInvoice.setBuyer(invoice.getBuyerInfo().getName());
            vnptInvoice.setCusAddress(invoice.getBuyerInfo().getAddress());
            vnptInvoice.setCusPhone(invoice.getBuyerInfo().getPhone());
            vnptInvoice.setSmsDeliver(invoice.getBuyerInfo().getPhone());
            vnptInvoice.setEmailDeliver(invoice.getBuyerInfo().getEmail());
            if (invoice.getBuyerInfo().getType() == InvoiceBuyerTypeEnum.BUSINESS) {
                vnptInvoice.setCusName(invoice.getBuyerInfo().getName());
                vnptInvoice.setCusTaxCode(invoice.getBuyerInfo().getTaxCode());
            } else {
                vnptInvoice.setCCCDan(invoice.getBuyerInfo().getCitizenId());
            }
        } else {
            vnptInvoice.setBuyer(CUSTOMER_NO_REQUEST_INVOICE_NO);
        }

        VnptInvoices invoices = VnptInvoices.builder()
                .inv(Collections.singletonList(
                        VnptInv.builder()
                                .key(fKey)
                                .invoice(vnptInvoice)
                                .build()))
                .build();

        return ImportAndPublishAssignedNoRequest.builder()
                .brandID(invoice.getBrandId())
                .account(vnptAccount)
                .acPass(vnptAcPass)
                .username(vnptUsername)
                .pass(vnptPassword)
                .pattern(invoice.getPatternNo())
                .serial(invoice.getSerialNo())
                .convert(0)
                .xmlInvData(invoices)
                .build();
    }

    private String buildFKey(String brandId, String patternNo, String serialNo, Long invoiceNo) {
        return String.format("%s%s%s%s%07d", companyAlias, brandId, patternNo.replaceAll("/", ""), serialNo.replaceAll("/", ""), invoiceNo);
    }

    private String buildSalt(String fKey) {
        return Base64Util.encode(fKey.substring(fKey.length() - 6));
    }

    private <T> String buildRequest(T requestBody) throws JsonProcessingException {
        String xml = xmlMapper.writeValueAsString(requestBody);
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" +
                "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                "    <soap:Body>\n" +
                "        %s\n" +
                "    </soap:Body>\n" +
                "</soap:Envelope>\n", xml);
    }

    private <T> T parseResponse(String xml, Class<T> responseType) {
        try {
            // 1) Parse XML to DOM
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true); // quan trọng để tìm theo localName
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));

            // 2) Find <Body>
            NodeList bodyNodes = document.getElementsByTagNameNS("*", "Body");
            if (bodyNodes.getLength() == 0) {
                throw new RuntimeException("No <Body> element found in SOAP XML");
            }
            Element body = (Element) bodyNodes.item(0);

            // 3) Lấy payload node (assume là element đầu tiên trong Body)
            Node payloadNode = null;
            NodeList children = body.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                Node node = children.item(i);
                if (node.getNodeType() == Node.ELEMENT_NODE) {
                    payloadNode = node;
                    break;
                }
            }
            if (payloadNode == null) {
                throw new RuntimeException("No payload found inside <Body>");
            }

            // 4) Convert payload Node -> String
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(payloadNode), new StreamResult(writer));
            String payloadXml = writer.toString();

            // 5) Convert XML String -> Object
            XmlMapper xmlMapper = new XmlMapper();
            return xmlMapper.readValue(payloadXml, responseType);

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse SOAP response", e);
        }
    }

    @JacksonXmlRootElement(localName = "PublishInvFkey")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class PublishInvFkeyRequest {

        @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
        private final String xmlns = "http://tempuri.org/";

        @JacksonXmlProperty(localName = "BrandID")
        private String brandID;

        @JacksonXmlProperty(localName = "Account")
        private String account;

        @JacksonXmlProperty(localName = "ACpass")
        private String acPass;

        @JacksonXmlProperty(localName = "username")
        private String username;

        @JacksonXmlProperty(localName = "password")
        private String pass;

        @JacksonXmlProperty(localName = "pattern")
        private String pattern;

        @JacksonXmlProperty(localName = "serial")
        private String serial;

        @JacksonXmlProperty(localName = "lsFkey")
        private List<String> fkeys;
    }

    @JacksonXmlRootElement(localName = "ImportInvByPattern")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class ImportInvByPatternRequest {

        @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
        private final String xmlns = "http://tempuri.org/";

        @JacksonXmlProperty(localName = "BrandID")
        private String brandID;

        @JacksonXmlProperty(localName = "Account")
        private String account;

        @JacksonXmlProperty(localName = "ACpass")
        private String acPass;

        @JacksonXmlProperty(localName = "username")
        private String username;

        @JacksonXmlProperty(localName = "password")
        private String pass;

        @JacksonXmlProperty(localName = "pattern")
        private String pattern;

        @JacksonXmlProperty(localName = "serial")
        private String serial;

        @JacksonXmlProperty(localName = "convert")
        private Integer convert;

        @JacksonXmlProperty(localName = "xmlInvData")
        @JsonSerialize(using = CDataObjectSerializer.class)
        private VnptInvoices xmlInvData;
    }


    @JacksonXmlRootElement(localName = "ImportAndPublishAssignedNo")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class ImportAndPublishAssignedNoRequest {

        @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
        private final String xmlns = "http://tempuri.org/";

        @JacksonXmlProperty(localName = "BrandID")
        private String brandID;

        @JacksonXmlProperty(localName = "Account")
        private String account;

        @JacksonXmlProperty(localName = "ACpass")
        private String acPass;

        @JacksonXmlProperty(localName = "username")
        private String username;

        @JacksonXmlProperty(localName = "password")
        private String pass;

        @JacksonXmlProperty(localName = "pattern")
        private String pattern;

        @JacksonXmlProperty(localName = "serial")
        private String serial;

        @JacksonXmlProperty(localName = "convert")
        private Integer convert;

        @JacksonXmlProperty(localName = "xmlInvData")
        @JsonSerialize(using = CDataObjectSerializer.class)
        private VnptInvoices xmlInvData;
    }

    @JacksonXmlRootElement(localName = "Invoices")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class VnptInvoices {

        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "Inv")
        private List<VnptInv> inv;
    }

    @JacksonXmlRootElement(localName = "Inv")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class VnptInv {

        @JacksonXmlProperty(localName = "key")
        private String key;

        @JacksonXmlProperty(localName = "Invoice")
        private VnptInvoice invoice;
    }

    @JacksonXmlRootElement(localName = "Invoice")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class VnptInvoice {

        @JacksonXmlProperty(localName = "salt")
        private String salt;

        @JacksonXmlProperty(localName = "InvoiceNo")
        private String invoiceNo;

        @JacksonXmlProperty(localName = "OrderNo")
        private String orderNo;

        @JacksonXmlProperty(localName = "OrderDate")
        private String orderDate;

        @JacksonXmlProperty(localName = "CusCode")
        private String cusCode;

        @JacksonXmlProperty(localName = "Buyer")
        private String buyer;

        @JacksonXmlProperty(localName = "CusName")
        private String cusName;

        @JacksonXmlProperty(localName = "CusAddress")
        private String cusAddress;

        @JacksonXmlProperty(localName = "CusPhone")
        private String cusPhone;

        @JacksonXmlProperty(localName = "CusTaxCode")
        private String cusTaxCode;

        @JacksonXmlProperty(localName = "PaymentMethod")
        private String paymentMethod;

        @JacksonXmlElementWrapper(localName = "Products")
        @JacksonXmlProperty(localName = "Product")
        private List<VnptProduct> products;

        @JacksonXmlProperty(localName = "GrossValue_NonTax")
        private BigDecimal grossValueNonTax;

        @JacksonXmlProperty(localName = "GrossValue")
        private BigDecimal grossValue;

        @JacksonXmlProperty(localName = "VatAmount0")
        private BigDecimal vatAmount0;

//        @JacksonXmlProperty(localName = "grossValue0")
//        private BigDecimal grossValue0;

        @JacksonXmlProperty(localName = "VatAmount5")
        private BigDecimal vatAmount5;

//        @JacksonXmlProperty(localName = "grossValue5")
//        private BigDecimal grossValue5;

        @JacksonXmlProperty(localName = "VatAmount8")
        private BigDecimal vatAmount8;

//        @JacksonXmlProperty(localName = "grossValue8")
//        private BigDecimal grossValue8;

        @JacksonXmlProperty(localName = "VatAmount10")
        private BigDecimal vatAmount10;

//        @JacksonXmlProperty(localName = "grossValue10")
//        private BigDecimal grossValue10;

        @JacksonXmlProperty(localName = "Total")
        private BigDecimal total;

        @JacksonXmlProperty(localName = "VATRate")
        private Integer vatRate;

        @JacksonXmlProperty(localName = "VATAmount")
        private BigDecimal vatAmount;

        @JacksonXmlProperty(localName = "Amount")
        private BigDecimal amount;

        @JacksonXmlProperty(localName = "AmountInWords")
        private String amountInWords;

        @JacksonXmlProperty(localName = "ArisingDate")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
        private LocalDateTime arisingDate;

        @JacksonXmlProperty(localName = "EmailDeliver")
        private String emailDeliver;

        @JacksonXmlProperty(localName = "SMSDeliver")
        private String smsDeliver;

        @JacksonXmlProperty(localName = "Extra")
        private String extra;

        @JacksonXmlProperty(localName = "Extra1")
        private String extra1;

        @JacksonXmlProperty(localName = "BusinessDate")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
        private LocalDateTime businessDate;

        @JacksonXmlProperty(localName = "CusBankNo")
        private String cusBankNo;

        @JacksonXmlProperty(localName = "CusBankName")
        private String cusBankName;

        @JacksonXmlProperty(localName = "CurrencyUnit")
        private String currencyUnit;

        @JacksonXmlProperty(localName = "ExchangeRate")
        private BigDecimal exchangeRate;

        @JacksonXmlProperty(localName = "MDVQHNSach")
        private String mDVQHNSach;

        @JacksonXmlProperty(localName = "CCCDan")
        private String CCCDan;

        @JacksonXmlProperty(localName = "SHChieu")
        private String SHChieu;

    }

    @JacksonXmlRootElement(localName = "Product")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class VnptProduct {

        @JacksonXmlProperty(localName = "Code")
        private String code;

        @JacksonXmlProperty(localName = "ProdName")
        private String prodName;

        @JacksonXmlProperty(localName = "ProdUnit")
        private String prodUnit;

        @JacksonXmlProperty(localName = "ProdQuantity")
        private Long prodQuantity;

        @JacksonXmlProperty(localName = "ProdPrice")
        private BigDecimal prodPrice;

        @JacksonXmlProperty(localName = "Total")
        private BigDecimal total;

        @JacksonXmlProperty(localName = "VATRate")
        private Integer vatRate;

        @JacksonXmlProperty(localName = "VATAmount")
        private BigDecimal vatAmount;

        @JacksonXmlProperty(localName = "Amount")
        private BigDecimal amount;

        @JacksonXmlProperty(localName = "IsSum")
        private Integer isSum;
    }

    @JacksonXmlRootElement(localName = "downloadInvPDFFkeyByStaff")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class DownloadInvPDFFkeyByStaff {

        @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
        private final String xmlns = "http://tempuri.org/";

        @JacksonXmlProperty(localName = "BrandID")
        private String brandID;

        @JacksonXmlProperty(localName = "userName")
        private String username;

        @JacksonXmlProperty(localName = "userPass")
        private String pass;

        @JacksonXmlProperty(localName = "fkey")
        private String fKey;
    }

    @JacksonXmlRootElement(localName = "checkFkey")
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class CheckFkey {

        @JacksonXmlProperty(isAttribute = true, localName = "xmlns")
        private final String xmlns = "http://tempuri.org/";

        @JacksonXmlProperty(localName = "BrandID")
        private String brandID;

        @JacksonXmlProperty(localName = "fkey")
        private String fKey;

        @JacksonXmlProperty(localName = "username")
        private String username;

        @JacksonXmlProperty(localName = "pass")
        private String pass;
    }

    public static class CDataObjectSerializer extends JsonSerializer<Object> implements ContextualSerializer {

        private final String localName;

        public CDataObjectSerializer() {
            this.localName = null;
        }

        public CDataObjectSerializer(String localName) {
            this.localName = localName;
        }

        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            String xml = xmlMapper.writeValueAsString(value);

            if (localName != null && !localName.isEmpty()) {
                gen.writeRaw("<" + localName + "><![CDATA[" + xml + "]]></" + localName + ">");
            } else {
                gen.writeRaw("<![CDATA[" + xml + "]]>");
            }
        }

        @Override
        public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty property) throws JsonMappingException {
            String localName = null;
            if (property != null) {
                JacksonXmlProperty anno = property.getAnnotation(JacksonXmlProperty.class);
                if (anno != null && !anno.localName().isEmpty()) {
                    localName = anno.localName();
                }
            }
            return new CDataObjectSerializer(localName);
        }
    }

}
