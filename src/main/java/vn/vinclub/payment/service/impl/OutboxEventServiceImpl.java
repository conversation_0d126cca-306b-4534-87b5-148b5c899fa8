package vn.vinclub.payment.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.dto.event.outbox.BaseOutboxEvent;
import vn.vinclub.payment.dto.event.outbox.IssueInvoiceEvent;
import vn.vinclub.payment.dto.event.outbox.PaymentStatusChangedEvent;
import vn.vinclub.payment.enums.OutboxStatusEnum;
import vn.vinclub.payment.model.OutboxEvent;
import vn.vinclub.payment.repository.OutboxEventRepository;
import vn.vinclub.payment.service.KafkaProducer;
import vn.vinclub.payment.service.OutboxEventService;

import java.util.Objects;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OutboxEventServiceImpl implements OutboxEventService {

    private final OutboxEventRepository outboxEventRepository;
    private final KafkaProducer kafkaProducer;

    @Value("${kafka.payment_history_status_changed.topic}")
    private String paymentStatusChangedKafkaTopic;

    @Value("${kafka.issue_invoice.topic}")
    private String issueInvoiceKafkaTopic;


    @Override
    public <T extends BaseOutboxEvent> void create(T event, String outboxEventId) {
        try (var p = new Profiler(getClass(), "create")) {
            OutboxEvent outboxEvent = new OutboxEvent();
            outboxEvent.setPayload(JsonUtils.toNode(event));
            outboxEvent.setEventId(outboxEventId);
            outboxEvent.setStatus(OutboxStatusEnum.WAITING);
            outboxEvent.setEventCode(event.getEventCode());
            outboxEvent.setMessageKey(event.getKafkaMessageKey());
            if (Objects.nonNull(event.getOutboxMetadata())) {
                outboxEvent.setMetadata(event.getOutboxMetadata());
            }
            outboxEventRepository.save(outboxEvent);
        }
    }

    @Override
    public void processPendingOutbox() {
        try (var p = new Profiler(getClass(), "processPendingOutbox")) {
            outboxEventRepository.findAllByStatusOrderByIdAsc(OutboxStatusEnum.WAITING)
                    .forEach(outboxEvent -> {
                        try {
                            makeSent(outboxEvent.getEventId(), outboxEvent);
                        } catch (Exception e) {
                            log.error("Error when processing pending outbox {}", JsonUtils.toString(outboxEvent), e);
                            throw new RuntimeException(e);
                        }
                    });
        }
    }

    @Override
    public void makeSent(String outboxEventId, OutboxEvent outboxEvent) throws ExecutionException, InterruptedException {
        try (var p = new Profiler(getClass(), "makeSent")) {
            if (outboxEvent == null) {
                outboxEvent = outboxEventRepository.findByEventId(outboxEventId).orElse(null);
            }

            if (outboxEvent == null) {
                log.error("[makeSent] Outbox event not found for ID: {}", outboxEventId);
                return;
            }

            if (outboxEvent.getStatus() != OutboxStatusEnum.WAITING) {
                log.warn("[makeSent] Outbox event id {} status is {}", outboxEvent.getEventId(), outboxEvent.getStatus());
                return;
            }

            String messageKey = outboxEvent.getMessageKey();
            String message = JsonUtils.toString(outboxEvent.getPayload());
            log.info("[makeSent] Sending message to kafka: {}", message);
            kafkaProducer.sendMessage(getTopicByOutboxEvent(outboxEvent), messageKey, message);

            outboxEvent.setEventId(outboxEventId);
            outboxEvent.setStatus(OutboxStatusEnum.SENT);
            outboxEventRepository.save(outboxEvent);
        }
    }

    private String getTopicByOutboxEvent(OutboxEvent outboxEvent) {
        return switch (outboxEvent.getEventCode()) {
            case PaymentStatusChangedEvent.EVENT_CODE -> paymentStatusChangedKafkaTopic;
            case IssueInvoiceEvent.EVENT_CODE -> issueInvoiceKafkaTopic;
            default -> throw new IllegalArgumentException("Unknown event code: " + outboxEvent.getEventCode());
        };
    }

}
