package vn.vinclub.payment.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.controller.request.CreateUpdatePaymentMethodDto;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.PaymentMethod;
import vn.vinclub.payment.repository.PaymentMethodRepository;
import vn.vinclub.payment.service.PaymentMethodService;
import vn.vinclub.payment.util.ServiceResponse;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class PaymentMethodServiceImpl implements PaymentMethodService {

    private final PaymentMethodRepository paymentMethodRepository;

    @Override
    @Transactional(readOnly = true)
    public List<PaymentMethod> getActivePaymentMethods() {
        try (var p = new Profiler(getClass(), "getActivePaymentMethods")) {
            return paymentMethodRepository.findAllByActiveIsTrueOrderByDisplayOrder();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PaymentMethod> optByIdAndActive(Long paymentMethodId) {
        try (var p = new Profiler(getClass(), "optByIdAndActive")) {
            return paymentMethodRepository.findByIdAndActiveTrue(paymentMethodId);
        }
    }

    @Override
    public PaymentMethod getByIdAndActive(Long paymentMethodId) {
        try (var p = new Profiler(getClass(), "getByIdAndActive")) {
            return paymentMethodRepository.findByIdAndActiveTrue(paymentMethodId)
                    .orElseThrow(() ->
                            new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_METHOD_NOT_EXIST)));
        }
    }

    @Override
    public Optional<PaymentMethod> optById(Long paymentMethodId) {
        try (var p = new Profiler(getClass(), "optById")) {
            return paymentMethodRepository.findById(paymentMethodId);
        }
    }

    @Override
    public List<PaymentMethod> findByIds(List<Long> paymentMethodIds) {
        try (var p = new Profiler(getClass(), "findByIds")) {
            return paymentMethodRepository.findByIdIn(paymentMethodIds);
        }
    }

    @Override
    public PaymentMethod updatePaymentMethod(Long id, CreateUpdatePaymentMethodDto updateReq) {
        try (var p = new Profiler(getClass(), "updatePaymentMethod")) {


            PaymentMethod paymentMethod = getByIdAndActive(id);

            if (updateReq.getPaymentMethodType() != null)
                paymentMethod.setPaymentMethodType(updateReq.getPaymentMethodType());
            if (updateReq.getStatus() != null)
                paymentMethod.setStatus(updateReq.getStatus());
            if (updateReq.getServiceProviderType() != null)
                paymentMethod.setServiceProviderType(updateReq.getServiceProviderType());
            if (updateReq.getServiceProviderId() != null)
                paymentMethod.setServiceProviderId(updateReq.getServiceProviderId());
            if (updateReq.getMinAmountAllowed() != null)
                paymentMethod.setMinAmountAllowed(updateReq.getMinAmountAllowed());
            if (updateReq.getMaxAmountAllowed() != null)
                paymentMethod.setMaxAmountAllowed(updateReq.getMaxAmountAllowed());
            if (updateReq.getDisplayOrder() != null)
                paymentMethod.setDisplayOrder(updateReq.getDisplayOrder());
            if (updateReq.getFeeRequired() != null)
                paymentMethod.setFeeRequired(updateReq.getFeeRequired());
            if (updateReq.getFixedFee() != null)
                paymentMethod.setFixedFee(updateReq.getFixedFee());
            if (updateReq.getFeePercentage() != null)
                paymentMethod.setFeePercentage(updateReq.getFeePercentage());
            if (updateReq.getDisplayNames() != null)
                paymentMethod.setDisplayNames(updateReq.getDisplayNames());
            if (updateReq.getFeeConfigs() != null)
                paymentMethod.setFeeConfigs(updateReq.getFeeConfigs());

            return paymentMethodRepository.save(paymentMethod);
        }

    }
}
