package vn.vinclub.payment.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.DateUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.PayGateCheckoutResult;
import vn.vinclub.payment.dto.PayGateIpnData;
import vn.vinclub.payment.dto.PayGatePaymentResult;
import vn.vinclub.payment.dto.galaxypay.GPayIpnReqDto;
import vn.vinclub.payment.dto.galaxypay.GPayReqPayData;
import vn.vinclub.payment.dto.galaxypay.GPayRequestDto;
import vn.vinclub.payment.dto.galaxypay.GPayRespPayData;
import vn.vinclub.payment.dto.galaxypay.GPayResponseDto;
import vn.vinclub.payment.dto.galaxypay.PgGPayCheckoutResult;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.PaymentStatusEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.service.PaymentGatewayService;
import vn.vinclub.payment.util.I18nUtil;
import vn.vinclub.payment.util.ServiceResponse;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> 12/11/24 17:35
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GalaxyPayServiceImpl extends BaseService implements PaymentGatewayService {

    @Value("${payment_gateway.galaxypay.api_v1_url}")
    private String apiV1Url;

    @Value("${payment_gateway.galaxypay.apikey}")
    private String apikey;

    @Value("${payment_gateway.galaxypay.salt}")
    private String salt;

    @Value("${payment_gateway.galaxypay.callback_app_url}")
    private String webviewCallbackUrlDefault;

    @Value("${payment_gateway.galaxypay.callback_ipn_url}")
    private String ipnUrlDefault;

    private final RestTemplate restTemplate;

    @Override
    public PaymentGatewayEnum getPayGateType() {
        return PaymentGatewayEnum.GALAXY_PAY;
    }

    private HttpHeaders getDefaultHeaders(String signature) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("apikey", apikey);
        headers.add("signature", signature);

        return headers;
    }

    @Override
    public PayGateCheckoutResult checkout(PaymentHistory paymentHistory) {

        ResponseEntity<String> exchangeResult = null;
        GPayRequestDto<GPayReqPayData> req = null;

        try (var p = new Profiler(getClass(), "checkout")) {

            Map<String, Object> orderDescriptionMapData = Map.of("paymentId", paymentHistory.getPaymentId());
            var orderDescription = I18nUtil.applyTemplate(
                    "galaxypay-payment-checkout-order-description",
                    orderDescriptionMapData,
                    getCurrVClubCustomerLang()
            );

            req = GPayRequestDto.<GPayReqPayData>builder()
                    .requestData(
                            GPayReqPayData.builder()
                                    .language(getCurrVClubCustomerLang())
                                    .apiOperation(getApiOperation(paymentHistory))
                                    .orderId(paymentHistory.getPaymentId())
                                    .orderAmount(paymentHistory.getTotalAmount())
                                    .orderCurrency("VND")
                                    .orderDateTime(DateUtils.convertLocalDateTimeToString(paymentHistory.getPaymentDate(), "yyyyMMddHHmmss"))
                                    .orderDescription(orderDescription)
                                    .paymentMethod(getPaymentMethodType(paymentHistory))
                                    .sourceType(getSourceType(paymentHistory, getPaymentMethodType(paymentHistory)))
                                    .extraData(JsonUtils.toString(Map.<String, Object>of(
                                                    "paymentId", Optional.ofNullable(paymentHistory.getPaymentId()).orElse(""),
                                                    "paymentMethodId", Optional.ofNullable(paymentHistory.getPaymentMethodId()).orElse(-1L),
                                                    "paymentMethodType", Optional.ofNullable(paymentHistory.getPaymentMethodType()).map(PaymentMethodEnum::getCode).orElse(""),
                                                    "serviceProviderId", Optional.ofNullable(paymentHistory.getServiceProviderId()).orElse(-1L),
                                                    "serviceProviderType", Optional.ofNullable(paymentHistory.getServiceProviderType()).map(PaymentServiceProviderEnum::getCode).orElse(""),
                                                    "pgwId", Optional.ofNullable(paymentHistory.getPgwId()).orElse(-1L),
                                                    "pgwType", Optional.ofNullable(paymentHistory.getPgwType()).map(PaymentGatewayEnum::getCode).orElse(""),
                                                    "transactionType", Optional.ofNullable(paymentHistory.getTransactionType()).map(TransactionTypeEnum::getCode).orElse("")
                                            ))
                                    )
                                    .successURL(
                                            webviewCallbackUrlDefault
                                                    .replaceAll("\\$txn_type", paymentHistory.getTransactionType().getCode())
                                                    .replaceAll("\\$txn_id", paymentHistory.getPaymentId())
                                                    .replaceAll("\\$status", PaymentStatusEnum.SUCCESS.getCode())
                                    )
                                    .failureURL(
                                            webviewCallbackUrlDefault
                                                    .replaceAll("\\$txn_type", paymentHistory.getTransactionType().getCode())
                                                    .replaceAll("\\$txn_id", paymentHistory.getPaymentId())
                                                    .replaceAll("\\$status", PaymentStatusEnum.FAILED.getCode())
                                    )
                                    .cancelURL(
                                            webviewCallbackUrlDefault
                                                    .replaceAll("\\$txn_type", paymentHistory.getTransactionType().getCode())
                                                    .replaceAll("\\$txn_id", paymentHistory.getPaymentId())
                                                    .replaceAll("\\$status", PaymentStatusEnum.CANCELLED.getCode())
                                    )
                                    .ipnURL(ipnUrlDefault)

                                    .build()
                    )
                    .build();

            String signature = createSignature(JsonUtils.toString(req));

            HttpEntity<Object> httpEntity = new HttpEntity<>(req, getDefaultHeaders(signature));

            exchangeResult = restTemplate.exchange(
                    apiV1Url + "/transaction/pay",
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            GPayResponseDto<GPayRespPayData> respDto = getResponseData(exchangeResult, new TypeReference<>() {
            });

            var result = PgGPayCheckoutResult.builder().build();
            result.setCheckoutUrl(respDto.getResponseData().getEndpoint());
            result.setPgwTxnId(respDto.getResponseData().getTransactionId());
            return result;

        } catch (HttpStatusCodeException e) {
            log.error("checkout({}): {}\t{} - {}\t{}",
                    JsonUtils.toString(paymentHistory), JsonUtils.toString(req),
                    e.getStatusCode().value(), e.getResponseBodyAsString(),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse("")
            );

            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR));
        } catch (BusinessLogicException e) {
            log.error("checkout({}): {}\t{}\t{}",
                    JsonUtils.toString(paymentHistory), JsonUtils.toString(req),
                    JsonUtils.toString(e.getPayload()),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse("")
            );

//            throw new BusinessLogicException(ServiceResponse.error(e.getPayload().getCode(), AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR.getMessage()));
            throw e;
        }
    }

    @Override
    public PayGatePaymentResult getTransactionByPgTxnId(String pgTxnId) {
        try (var p = new Profiler(getClass(), "getTransactionByPgTxnId")) {
            ResponseEntity<String> exchangeResult = null;
            GPayRequestDto<Map<String, Object>> req = null;
            try {
                req = GPayRequestDto.<Map<String, Object>>builder()
                        .requestData(Collections.singletonMap("transactionID", pgTxnId))
                        .build();

                String signature = createSignature(JsonUtils.toString(req));

                HttpEntity<Object> httpEntity = new HttpEntity<>(req, getDefaultHeaders(signature));

                exchangeResult = restTemplate.exchange(
                        apiV1Url + "/transaction/query",
                        HttpMethod.POST,
                        httpEntity,
                        String.class
                );

                GPayResponseDto<ObjectNode> respDto = getResponseData(exchangeResult, new TypeReference<>() {
                });

                return parseTxnDetail(respDto.getResponseData());

            } catch (HttpStatusCodeException e) {
                log.error("getTransactionByPgTxnId({}): {}\t{} - {}\t{}",
                        pgTxnId, JsonUtils.toString(req),
                        e.getStatusCode().value(), e.getResponseBodyAsString(),
                        Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse("")
                );

                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR));
            } catch (BusinessLogicException e) {
                log.error("getTransactionByPgTxnId({}): {}\t{}\t{}",
                        pgTxnId, JsonUtils.toString(req),
                        JsonUtils.toString(e.getPayload()),
                        Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse("")
                );

                throw new BusinessLogicException(ServiceResponse.error(e.getPayload().getCode(), AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR.getMessage()));
            }
        }
    }

    @Override
    public PayGatePaymentResult processIPN(PayGateIpnData ipnData) {
        try (var p = new Profiler(getClass(), "processIPN")) {
            var data = (GPayIpnReqDto) ipnData;

            JsonNode obj = JsonUtils.stringToJsonNode(
                    new String(Base64.getDecoder().decode(data.getData()), StandardCharsets.UTF_8)
            ).path("responseData");

            return getTransactionByPgTxnId(parseTxnDetail(obj).getPgwTxnId());

        } catch (Exception e) {
            log.error("processIPN({}): {}", ipnData, e.getMessage());
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_BAD_REQUEST));
        }
    }

    @Override
    public boolean isValidIPN(PayGateIpnData ipnData) {

        if (!(ipnData instanceof GPayIpnReqDto data)) {
            return false;
        }

        return data.getSignature().equals(createSignature(data.getData()));
    }

    private PayGatePaymentResult parseTxnDetail(JsonNode obj) {
        try (var p = new Profiler(getClass(), "parseTxnDetail")) {
            String pgwTxnStage = obj.path("transactionStage").asText("");
            String pwgTxnDescription = obj.path("transactionDescription").asText("");

            PaymentStatusEnum status;
            switch (pgwTxnStage) {
                case "PROCESSING":
                case "INIT":
                    status = PaymentStatusEnum.PROCESSING;
                    break;
                case "SUCCESSFUL":
                    status = PaymentStatusEnum.SUCCESS;
                    break;
                case "FAILURE":
                    if (pwgTxnDescription.contains("Transaction is cancelled"))
                        status = PaymentStatusEnum.CANCELLED;
                    else
                        status = PaymentStatusEnum.FAILED;
                    break;
                default:
                    status = PaymentStatusEnum.PROCESSING;
            }

            return PayGatePaymentResult.builder()
                    .paymentId(obj.path("orderID").asText())
                    .paymentDate(DateUtils.convertStringToLocalDateTime(obj.path("orderDateTime").asText(), "yyyyMMddHHmmss"))
                    .description(obj.path("orderDescription").asText(""))
                    .status(status)

                    .amount(BigDecimal.valueOf(obj.path("orderAmount").asDouble()))
                    .currency(obj.path("orderCurrency").asText(""))

                    .pgwTxnId(obj.path("transactionID").asText())
                    .pgwTxnStage(pgwTxnStage)
                    .pgwTxnDescription(pwgTxnDescription)

                    .build();
        }
    }

    private <T> GPayResponseDto<T> getResponseData(ResponseEntity<String> exchangeResult, TypeReference<GPayResponseDto<T>> typeReference) {
        switch (exchangeResult.getStatusCode().value()) {
            case 200:
                GPayResponseDto<T> respDto = JsonUtils.toObject(exchangeResult.getBody(), typeReference);

                if (respDto.isSuccess()) {
                    return respDto;
                } else {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_BAD_REQUEST.getCode(), respDto.getResponseMessage()));
                }

            case 400:
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_BAD_REQUEST.getCode(), exchangeResult.getBody()));
            default:
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR.getCode(), exchangeResult.getBody()));
        }
    }

    private String getPaymentMethodType(PaymentHistory paymentHistory) {
        return switch (paymentHistory.getPaymentMethodType()) {
            case null ->
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "paymentMethodType"));
            case PaymentMethodEnum.E_WALLET -> "WALLET";
            case PaymentMethodEnum.QR_PAYMENT -> "QRPAY";
            case PaymentMethodEnum.ATM_CARD, PaymentMethodEnum.BANK_TRANSFER, PaymentMethodEnum.MOBILE_BANKING,
                    PaymentMethodEnum.DOMESTIC_CARD -> "DOMESTIC";
            case PaymentMethodEnum.INTERNATIONAL_CARD -> "INTERNATIONAL";
            default ->
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INVALID_PARAM, "paymentMethodType"));
        };
    }

    private String getSourceType(PaymentHistory paymentHistory, String gPayMethod) {
        return switch (gPayMethod) {
            case "DOMESTIC" -> null;
            case "INTERNATIONAL" -> null;
//            case "INTERNATIONAL" -> {
//                yield switch (transactionHistory.getServiceProviderType()) {
//                    case null -> null;
//                    case PaymentServiceProviderEnum.VISA -> "VISA";
//                    case PaymentServiceProviderEnum.MASTERCARD -> "MASTER";
//                    default -> null;
//                };
//            }
            case "WALLET" -> {
                yield switch (paymentHistory.getServiceProviderType()) {
                    case null -> null;
                    case PaymentServiceProviderEnum.MOMO -> PaymentServiceProviderEnum.MOMO.getCode();
                    case PaymentServiceProviderEnum.ZALOPAY -> PaymentServiceProviderEnum.ZALOPAY.getCode();
                    case PaymentServiceProviderEnum.VIETTEL -> PaymentServiceProviderEnum.VIETTEL.getCode();
                    case PaymentServiceProviderEnum.GALAXYPAY -> PaymentServiceProviderEnum.GALAXYPAY.getCode();
                    default -> null;
                };
            }
            case "QRPAY" -> {
                yield switch (paymentHistory.getServiceProviderType()) {
                    case null -> null;
                    case PaymentServiceProviderEnum.VIETQR -> "QRPAY";
                    default -> null;
                };
            }
            default -> null;
        };
    }

    private String getApiOperation(PaymentHistory paymentHistory) {
        return "PAY";
    }

    public String createSignature(String payload) {
        return generateSHA256Hash(payload + salt);
    }

    private static String generateSHA256Hash(String input) {

        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            hexString.append(String.format("%02x", b));
        }

        return hexString.toString();
    }
}
