package vn.vinclub.payment.service.impl;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.common.util.LangUtils;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.controller.request.customer.GetPaymentHistoryRequest;
import vn.vinclub.payment.controller.request.customer.PaymentMethodRequest;
import vn.vinclub.payment.dto.*;
import vn.vinclub.payment.dto.internal.CoreCustomerDto;
import vn.vinclub.payment.dto.internal.CoreCustomerIdentifyRequest;
import vn.vinclub.payment.dto.invoice.CreateInvoiceRequest;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.enums.*;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.mapper.PaymentHistoryMapper;
import vn.vinclub.payment.model.Invoice;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.model.PaymentMethod;
import vn.vinclub.payment.service.InternalService;
import vn.vinclub.payment.service.InvoiceService;
import vn.vinclub.payment.service.KafkaProducer;
import vn.vinclub.payment.service.PaymentGatewayService;
import vn.vinclub.payment.service.PaymentHistoryService;
import vn.vinclub.payment.service.PaymentMethodService;
import vn.vinclub.payment.service.PaymentService;
import vn.vinclub.payment.service.QuotaService;
import vn.vinclub.payment.util.FeeCalculatorUtil;
import vn.vinclub.payment.util.I18nUtil;
import vn.vinclub.payment.util.PaymentUtil;
import vn.vinclub.payment.util.ServiceResponse;
import vn.vinclub.payment.util.TimeUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 12/11/24 17:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentServiceImpl extends BaseService implements PaymentService {

    @Value("${point.product.name}")
    private String pointProductName;

    @Value("${point.product.unit.name}")
    private String pointProductUnitName;

    @Value("${point.vat.rate}")
    private BigDecimal pointVATRate;

    @Value("${vclub_exchange_rate.point.per.unit.amount}")
    private Long pointPerUnitAmount;

    @Value("${vclub_exchange_rate.unit.amount}")
    private BigDecimal unitAmount;

    @Value("${config.topup-point.limit.transaction.daily.default:5}")
    private Long topupPointLimitTransactionDaily;

    @Value("${config.topup-point.limit.daily.default:0}")
    private Long topupPointLimitDaily;

    @Value("${config.topup-point.limit.monthly.default:0}")
    private Long topupPointLimitMonthly;

    @Value("#{'${config.topup-point.allowed-tier-ids:1,2,3,4}'.split(',')}")
    private List<Long> allowedSenderTierIdsConfig;

    @Value("#{'${config.topup-point.whitelist-customer-ids:}'.split(',')}")
    private List<Long> whitelistCustomerId;

    private List<Long> allowedSenderTierIds;

    @Value("${config.topup-point.min-point-allowed:50}")
    private Long minPointAllowed;

    @Value("${config.topup-point.max-point-allowed:5000}")
    private Long maxPointAllowed;

    @Value("${config.idempotent.ttl_second:5}")
    private Long idempotentTtlInSecond;

    @Value("${config.topup-point.limit.transaction.daily.ttl:30}")
    private Long topupPointLimitTransactionDailyTtl;

    private final KafkaProducer kafkaProducer;
    private final InvoiceService invoiceService;
    private final QuotaService quotaService;
    private final PaymentMethodService paymentMethodService;
    private final PaymentHistoryService paymentHistoryService;
    private final PaymentGatewayService paymentGatewayService;
    private final InternalService internalService;
    private final RedissonClient redissonClient;

    private final PaymentHistoryMapper paymentHistoryMapper;

    @PostConstruct
    public void init() {
        allowedSenderTierIds = allowedSenderTierIdsConfig.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public CheckoutPreviewDto calculateCheckout(CheckoutPaymentRequest checkoutPaymentRequest) {
        try (var p = new Profiler(getClass(), "calculateCheckout")) {

            checkCustomerEligibility(getCurrVClubCustomer().getId());
            internalService.checkTopupPointPaymentBlacklist(getCurrVClubCustomer().getId());

            var paymentMethod = paymentMethodService.getByIdAndActive(checkoutPaymentRequest.getPaymentMethodId());

            List<FeeDetail> feeDetails = Collections.emptyList();
            BigDecimal totalFee = BigDecimal.ZERO;
            if (BooleanUtils.isTrue(paymentMethod.getFeeRequired())) {
                feeDetails = FeeCalculatorUtil.calculateFees(checkoutPaymentRequest.getAmount(), paymentMethod.getFeeConfigs());
                totalFee = feeDetails.stream().map(FeeDetail::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            checkoutPaymentRequest.setFeeAmount(totalFee);
            checkoutPaymentRequest.setFeeDetails(feeDetails);

            checkoutPaymentRequest.setDiscountAmount(BigDecimal.ZERO);

            BigDecimal totalAmount = checkoutPaymentRequest.getAmount()
                    .add(checkoutPaymentRequest.getFeeAmount())
                    .subtract(checkoutPaymentRequest.getDiscountAmount());
            checkoutPaymentRequest.setTotalAmount(totalAmount);

            validatePaymentCheckout(checkoutPaymentRequest, paymentMethod, pointPerUnitAmount, unitAmount);

            return CheckoutPreviewDto.builder()
                    .paymentMethodId(paymentMethod.getId())
                    .paymentMethodName(LangUtils.getMapValueByLang(paymentMethod.getDisplayNames(), getCurrVClubCustomer().getLang(), AppConst.DEFAULT_LANGUAGE))
                    .pointPerUnitAmount(pointPerUnitAmount)
                    .unitAmount(unitAmount)
                    .amount(checkoutPaymentRequest.getAmount())
                    .totalAmount(totalAmount)
                    .feeAmount(checkoutPaymentRequest.getFeeAmount())
                    .feeDetails(checkoutPaymentRequest.getFeeDetails().stream().map(f -> FeeDetailDto.fromFeeDetail(f, getCurrVClubCustomerLang())).toList())
                    .discountAmount(checkoutPaymentRequest.getDiscountAmount())
                    .currency(checkoutPaymentRequest.getCurrency())
                    .build();
        }
    }

    @Override
    @Transactional
    public PaymentHistoryDto checkout(CheckoutPaymentRequest checkoutPaymentRequest) {
        try (var p = new Profiler(getClass(), "checkout")) {

            Function<String, Void> applyIdempotentFn = validateIdempotent(checkoutPaymentRequest);

            checkCustomerEligibility(getCurrVClubCustomer().getId());
            internalService.checkTopupPointPaymentBlacklist(getCurrVClubCustomer().getId());

            var paymentMethod = paymentMethodService.getByIdAndActive(checkoutPaymentRequest.getPaymentMethodId());


            List<FeeDetail> feeDetails = Collections.emptyList();
            BigDecimal totalFee = BigDecimal.ZERO;
            if (BooleanUtils.isTrue(paymentMethod.getFeeRequired())) {
                feeDetails = FeeCalculatorUtil.calculateFees(checkoutPaymentRequest.getAmount(), paymentMethod.getFeeConfigs());
                totalFee = feeDetails.stream().map(FeeDetail::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            checkoutPaymentRequest.setFeeAmount(totalFee);
            checkoutPaymentRequest.setFeeDetails(feeDetails);

            checkoutPaymentRequest.setDiscountAmount(BigDecimal.ZERO);

            BigDecimal totalAmount = checkoutPaymentRequest.getAmount()
                    .add(checkoutPaymentRequest.getFeeAmount())
                    .subtract(checkoutPaymentRequest.getDiscountAmount());
            checkoutPaymentRequest.setTotalAmount(totalAmount);

            checkoutPaymentRequest.setPointPerUnitAmount(pointPerUnitAmount);
            checkoutPaymentRequest.setUnitAmount(unitAmount);

            validatePaymentCheckout(checkoutPaymentRequest, paymentMethod, pointPerUnitAmount, unitAmount);

            InvoiceBuyerInfo invoiceBuyerInfo = null;
            if (checkoutPaymentRequest.getInvoiceInfo() != null) {
                invoiceBuyerInfo = invoiceService.validateInvoiceBuyerInfo(getCurrVClubCustomer().getId(), InvoiceBuyerInfo.fromCustomerDto(checkoutPaymentRequest.getInvoiceInfo()));
            }

            PaymentHistory pH = paymentHistoryService.initCheckoutPayment(checkoutPaymentRequest, invoiceBuyerInfo);
            applyIdempotentFn.apply(pH.getPaymentId());

            PayGateCheckoutResult checkoutResult = paymentGatewayService.checkout(pH);

            pH = paymentHistoryService.updatePaymentInfoWithCheckoutResult(pH.getPaymentId(), checkoutResult);

            var dto = PaymentHistoryDto.toDto(pH);
            dto.setPaymentLink(checkoutResult.getCheckoutUrl());

            return dto;
        }
    }

    private Function<String, Void> validateIdempotent(CheckoutPaymentRequest checkoutPaymentRequest) {
        String idempotentKey = PaymentUtil.generateIdempotentKey(getCurrVClubCustomer().getId(), checkoutPaymentRequest);
        RMapCache<String, String> mapCache = redissonClient.getMapCache(AppConst.REDIS_LOCK.IDEMPOTENT_CHECKOUT_PAYMENT);
        if (mapCache.containsKey(idempotentKey)) {
            log.debug("validateIdempotent({}) Duplicate with paymentId = {}", JsonUtils.toString(checkoutPaymentRequest), mapCache.get(idempotentKey));
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.CHECKOUT_PAYMENT_DUPLICATED));
        }

        return (paymentId) -> {
            mapCache.put(idempotentKey, paymentId, idempotentTtlInSecond, TimeUnit.SECONDS);
            return null;
        };

    }

    @Override
    @Transactional(readOnly = true)
    public List<PaymentMethodDto> getAvailablePaymentMethods(PaymentMethodRequest paymentMethodRequest) {
        try (var p = new Profiler(getClass(), "getAvailablePaymentMethods")) {
            List<PaymentMethod> paymentMethods = paymentMethodService.getActivePaymentMethods();
            PaymentHistory paymentHistory = paymentHistoryService
                    .optLatestSuccessPaymentHistoryByCustomerId(getCurrVClubCustomer().getId())
                    .orElse(null);
            return paymentMethods.stream()
                    .filter(e -> e.getStatus() != PaymentMethodStatusEnum.INACTIVE)
                    .map(paymentMethod -> {
                                var pm = PaymentMethodDto.toDto(paymentMethod, getCurrVClubCustomer().getLang(), pointPerUnitAmount, unitAmount);
                                pm.setLastUsed(Objects.nonNull(paymentHistory)
                                        && paymentHistory
                                        .getPaymentMethodId()
                                        .equals(paymentMethod.getId()));
                                return pm;
                            }
                    )
                    .toList();
        }
    }

    @Override
    public Page<PaymentHistoryDto> getPaymentHistories(GetPaymentHistoryRequest req, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getPaymentHistories")) {
            Page<PaymentHistory> transactionHistories = paymentHistoryService.getPaymentHistoriesByStatus(
                    req.getStatus(), getCurrVClubCustomer().getId(), pageable);
            Map<Long, PaymentMethod> paymentMethods = paymentMethodService.findByIds(
                            transactionHistories.stream().map(PaymentHistory::getPaymentMethodId).toList())
                    .stream()
                    .collect(Collectors.toMap(PaymentMethod::getId, Function.identity()));

            return transactionHistories.map(ph -> {
                var dto = PaymentHistoryDto.toDto(ph);
                var paymentMethod = paymentMethods.get(ph.getPaymentMethodId());
                dto.setPaymentMethodName(LangUtils.getMapValueByLang(paymentMethod.getDisplayNames(), getCurrVClubCustomer().getLang(), AppConst.DEFAULT_LANGUAGE));
                return dto;
            });
        }
    }

    @Override
    public PaymentHistoryDto getPaymentHistoryByPaymentId(String paymentId) {
        try (var p = new Profiler(getClass(), "getPaymentHistoryByPaymentId")) {
            Long customerId = getCurrVClubCustomer().getId();
            PaymentHistory transactionHistory = paymentHistoryService.optPaymentHistoryByPaymentId(paymentId)
                    .orElseThrow(() -> new BusinessLogicException(
                            ServiceResponse.error(
                                    AppErrorCode.NOT_FOUND, "Mã giao dịch", "mã giao dịch", paymentId)));

            if (!customerId.equals(transactionHistory.getCustomerId())) {
                throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_UNAUTHORIZED));
            }

            var paymentMethod = paymentMethodService.optById(transactionHistory.getPaymentMethodId())
                    .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_METHOD_NOT_EXIST)));
            var dto = PaymentHistoryDto.toDto(transactionHistory);
            dto.setPaymentMethodName(LangUtils.getMapValueByLang(paymentMethod.getDisplayNames(), getCurrVClubCustomer().getLang(), AppConst.DEFAULT_LANGUAGE));
            return dto;
        }
    }

    @Override
    public Page<FullPaymentHistoryDto> getFullPaymentHistories(PaymentHistoryFilter filter, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getFullPaymentHistories")) {
            var result = paymentHistoryService.getFullPaymentHistories(filter, pageable);
            return result.map(paymentHistoryMapper::toFullPaymentHistoryDTO);
        }
    }

    @Override
    public void syncPaymentStatusFromPaymentGateway(long gtId) {
        int size = 10;
        List<PaymentHistory> pendingPayments;
        try (var p = new Profiler(getClass(), "syncPaymentStatusFromPaymentGateway")) {
            LocalDateTime now = LocalDateTime.now().minusSeconds(10);
            pendingPayments = paymentHistoryService.getPendingPayments(now, size, gtId);

            pendingPayments.forEach(pendingPayment -> {
                try {
                    var paymentResult = paymentGatewayService.getTransactionByPgTxnId(pendingPayment.getPgwTxnId());
                    paymentHistoryService.updatePaymentInfo(pendingPayment.getPaymentId(), paymentResult, AppConst.CRON_JOB_PAYMENT_SYNC);
                } catch (Exception e) {
                    log.error("syncPaymentStatusFromPaymentGateway {}", e.getMessage(), e);
                }
            });
        }

        if (pendingPayments.size() == size) {
            syncPaymentStatusFromPaymentGateway(pendingPayments.get(size - 1).getId());
        }
    }

    @Override
    public FullPaymentHistoryDto getFullPaymentHistoryByPaymentId(String paymentId) {
        var result = paymentHistoryService.getPaymentHistoryByPaymentId(paymentId);
        return paymentHistoryMapper.toFullPaymentHistoryDTO(result);
    }

    @Override
    public CustomerPaymentConfigDto getConfigs(Long customerId) {
        checkCustomerEligibility(customerId);

        return CustomerPaymentConfigDto.builder()
                .minPointAllowed(minPointAllowed)
                .maxPointAllowed(maxPointAllowed)
                .build();
    }

    @Override
    public void validatePaymentQuota(TransactionTypeEnum transactionTypeEnum, Long customerId) {
        try (var p = new Profiler(getClass(), "validatePaymentQuota")) {
            if (topupPointLimitTransactionDaily > 0) {
                PaymentHistoryFilter filter = new PaymentHistoryFilter();
                filter.setStatuses(Arrays.asList(PaymentStatusEnum.SUCCESS, PaymentStatusEnum.PROCESSING));
                filter.setTransactionType(transactionTypeEnum);
                filter.setCreatedOnFrom(TimeUtil.getStartDateByLimitType(AppConst.POPUP_POINT_LIMIT_TYPE.DAILY).atStartOfDay());

                var result = quotaService.tryIncreaseQuotaWithLimit("topup-transaction-daily",
                        1,
                        topupPointLimitTransactionDaily,
                        () -> paymentHistoryService.countPaymentHistoriesByFilter(filter),
                        topupPointLimitTransactionDailyTtl);
                if (!result) {
                    throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.TOPUP_POINT_QUOTA_EXCEEDED_TRANSACTION_DAILY_ALL));
                }
            }
        }
    }

    private void validatePaymentCheckout(CheckoutPaymentRequest checkoutPaymentRequest,
                                         PaymentMethod paymentMethod,
                                         Long pointPerUnitAmount,
                                         BigDecimal unitAmount) {

        if (paymentMethod.getStatus() != PaymentMethodStatusEnum.ACTIVE) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.PAYMENT_METHOD_NOT_ACTIVE));
        }

        if (checkoutPaymentRequest.getPoint() < minPointAllowed || checkoutPaymentRequest.getPoint() > maxPointAllowed) {
            log.warn("[POINT_AMOUNT_NOT_ALLOWED] point {}", checkoutPaymentRequest.getPoint());
            throw new BusinessLogicException(ServiceResponse.error(
                    AppErrorCode.POINT_AMOUNT_NOT_ALLOWED, minPointAllowed, maxPointAllowed));
        }

        if (checkoutPaymentRequest.getAmount().compareTo(paymentMethod.getMinAmountAllowed()) < 0
                || checkoutPaymentRequest.getAmount().compareTo(paymentMethod.getMaxAmountAllowed()) > 0) {
            log.warn("[MONEY_AMOUNT_NOT_ALLOWED] amount {}", checkoutPaymentRequest.getAmount());
            throw new BusinessLogicException(ServiceResponse.error(
                    AppErrorCode.MONEY_AMOUNT_NOT_ALLOWED, paymentMethod.getMinAmountAllowed(), paymentMethod.getMaxAmountAllowed()));
        }

        BigDecimal expectedAmount = pointToAmount(checkoutPaymentRequest.getPoint(), pointPerUnitAmount, unitAmount);

        if (BooleanUtils.isTrue(paymentMethod.getFeeRequired())) {
            List<FeeDetail> feeDetails = FeeCalculatorUtil.calculateFees(expectedAmount, paymentMethod.getFeeConfigs());
            BigDecimal totalFee = feeDetails.stream().map(FeeDetail::getFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            expectedAmount = expectedAmount.add(totalFee);
        }

        if (expectedAmount.compareTo(checkoutPaymentRequest.getTotalAmount()) != 0) {
            log.warn("[MONEY_AMOUNT_NOT_MATCH] expectedAmount {} != {}", expectedAmount, checkoutPaymentRequest.getTotalAmount());
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.MONEY_AMOUNT_NOT_MATCH));
        }

        if (topupPointLimitDaily > 0) {
            Long totalPointInDay = paymentHistoryService.getPointTopupByCustomer(getCurrVClubCustomer().getId(), AppConst.POPUP_POINT_LIMIT_TYPE.DAILY);
            if (totalPointInDay + checkoutPaymentRequest.getPoint() > topupPointLimitDaily) {
                throw new BusinessLogicException(ServiceResponse.error(CustomerPaymentConfigDto.QuotaConfig.builder()
                                .limitPoint(topupPointLimitDaily)
                                .remainingPoint(topupPointLimitDaily - totalPointInDay)
                                .build(),
                        AppErrorCode.TOPUP_LIMIT_EXCEEDED_DAILY));
            }
        }
        if (topupPointLimitMonthly > 0) {
            Long totalPointInMonth = paymentHistoryService.getPointTopupByCustomer(getCurrVClubCustomer().getId(), AppConst.POPUP_POINT_LIMIT_TYPE.MONTLY);
            if (totalPointInMonth + checkoutPaymentRequest.getPoint() > topupPointLimitMonthly) {
                throw new BusinessLogicException(ServiceResponse.error(CustomerPaymentConfigDto.QuotaConfig.builder()
                                .limitPoint(topupPointLimitMonthly)
                                .remainingPoint(topupPointLimitMonthly - totalPointInMonth)
                                .build(),
                        AppErrorCode.TOPUP_LIMIT_EXCEEDED_MONTHLY));
            }
        }

    }

    private BigDecimal pointToAmount(long point, long pointPerUnitAmount, BigDecimal unitAmount) {
        return BigDecimal.valueOf(point / pointPerUnitAmount).multiply(unitAmount);
    }

    private void checkCustomerEligibility(Long customerId) {
        List<TopupRejectReasonEnum> result = new ArrayList<>();
        CoreCustomerDto coreCustomerDto = internalService.optCustomer(CoreCustomerIdentifyRequest.builder().vclubUserId(customerId).build())
                .orElseThrow(() -> new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_INVALID)));
        if (!coreCustomerDto.isIdentityDocumentVerified()) {
            result.add(TopupRejectReasonEnum.CUSTOMER_NOT_EKYC);
        }
        // whitelist must be checked after identity document verification
        if (!CollectionUtils.isEmpty(whitelistCustomerId) && whitelistCustomerId.contains(customerId)) {
            log.info("checkCustomerEligibility({}) customer is in whitelist", customerId);
            return;
        }
        if (!CollectionUtils.isEmpty(allowedSenderTierIds) && !allowedSenderTierIds.contains(coreCustomerDto.getTierId())) {
            result.add(TopupRejectReasonEnum.CUSTOMER_INSUFFICIENT_TIER);
        }
        if (!result.isEmpty()) {
            throw new BusinessLogicException(ServiceResponse.error(new TopupPointRejectDto(result), AppErrorCode.TOPUP_NOT_ELIGIBILITY));
        }
    }

    @Override
    public void createDraftPaymentInvoice(String paymentId, InvoiceSourceEnum invoiceSource) {
        PaymentHistory paymentHistory = paymentHistoryService.getPaymentHistoryByPaymentId(paymentId);

        if (paymentHistory.getStatus() != PaymentStatusEnum.SUCCESS) {
            log.warn("createDraftPaymentInvoice({}) payment not success, status = {}", paymentId, paymentHistory.getStatus());
            return;
        }

        List<Invoice.InvoiceProduct> items = new ArrayList<>();
        if (paymentHistory.getTransactionType() == TransactionTypeEnum.TOPUP_POINT) {
            CheckoutPaymentDataRequest dataRequest = JsonUtils.convertValue(paymentHistory.getDataRequest(), CheckoutPaymentDataRequest.class);

            // calculate VAT
            Integer vatPercent = pointVATRate.multiply(BigDecimal.valueOf(100)).intValue();
            BigDecimal unitPrice = unitAmount.divide(BigDecimal.ONE.add(pointVATRate), 0, RoundingMode.HALF_UP); // before VAT
            Long quantity = dataRequest.getPoint();
            BigDecimal amount = unitPrice.multiply(BigDecimal.valueOf(quantity)); // before VAT
            BigDecimal totalAmount = paymentHistory.getAmount(); // after VAT
            BigDecimal vatAmount = totalAmount.subtract(amount); // after VAT - before VAT = VAT

            items.add(Invoice.InvoiceProduct.builder()
                    .productName(pointProductName)
                    .productUnit(pointProductUnitName)
                    .quantity(quantity)
                    .unitPrice(unitPrice)
                    .amount(amount)
                    .vatRate(vatPercent)
                    .vatAmount(vatAmount)
                    .totalAmount(totalAmount)
                    .build());
        } else {
            throw new UnsupportedOperationException("Unsupported transaction type for invoice creation: " + paymentHistory.getTransactionType());
        }

        // TODO: SangLNN - Add VAT to Fee
        paymentHistory.getFeeDetails().forEach(feeDetail -> {
            items.add(Invoice.InvoiceProduct.builder()
                    .productName(I18nUtil.applyTemplate(feeDetail.getFeeType().getTitle(), null, null))
                    .quantity(1L)
                    .productUnit(feeDetail.getFeeType().getInvoiceUnit())
                    .unitPrice(feeDetail.getFeeAmount())
                    .amount(feeDetail.getFeeAmount())
                    .vatRate(-1)
                    .vatAmount(BigDecimal.ZERO)
                    .totalAmount(feeDetail.getFeeAmount())
                    .build());
        });

        CreateInvoiceRequest createInvoiceRequest = CreateInvoiceRequest.builder()
                .transactionType(InvoiceTransactionTypeEnum.fromTransactionTypeEnum(paymentHistory.getTransactionType()))
                .transactionId(paymentHistory.getPaymentId())
                .customerId(paymentHistory.getCustomerId())
                .buyerInfo(paymentHistory.getInvoiceBuyerInfo())
                .source(InvoiceSourceEnum.AUTO)
                .items(items)
                .currency(paymentHistory.getCurrency())
                .build();

        invoiceService.createDraftInvoice(createInvoiceRequest);
    }

}
