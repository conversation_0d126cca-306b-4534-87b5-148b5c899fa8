package vn.vinclub.payment.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.dto.event.historical.HistoricalActionEnum;
import vn.vinclub.payment.dto.event.historical.HistoricalEvent;
import vn.vinclub.payment.model.BaseEntity;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.service.KafkaProducer;
import vn.vinclub.payment.service.LogHistoricalService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogLogHistoricalServiceImpl implements LogHistoricalService {

    private final KafkaProducer kafkaProducer;
    private final ThreadLocal<Map<Class<?>, List<HistoricalEvent<? extends BaseEntity>>>> historicalEventThreadLocal = ThreadLocal.withInitial(HashMap::new);

    @Value("${kafka.historical.payment_history.topic.name:historical.payment_history}")
    private String paymentHistoryTopic;

    private final String DEFAULT_HISTORICAL_EVENT_TOPIC = "historical.untracked_entity";

    private final TransactionSynchronization txSynchronization = new TransactionSynchronization() {
        @Override
        public void afterCompletion(int status) {
            try {
                if (status == TransactionSynchronization.STATUS_COMMITTED) {
                    Map<Class<?>, List<HistoricalEvent<? extends BaseEntity>>> classHistoricalEventMap = historicalEventThreadLocal.get();
                    classHistoricalEventMap.forEach((k, v) -> {
                        log.info("[HistoricalEvent] Historical for {} processed, {} events sent", k.getSimpleName(), v.size());
                        v.forEach(LogLogHistoricalServiceImpl.this::_logHistoricalEvent);
                    });
                }

            } finally {
                historicalEventThreadLocal.remove();
            }
        }
    };

    private <T extends BaseEntity> HistoricalEvent<T> buildHistoricalEvent(T oldObj, T newObj, Long timestamp, HistoricalActionEnum action) {
        HistoricalEvent<T> historicalEvent = new HistoricalEvent<>();
        historicalEvent.setOldData(oldObj);
        historicalEvent.setData(newObj);
        historicalEvent.setTimestamp(Objects.requireNonNullElseGet(timestamp, System::currentTimeMillis));
        historicalEvent.setAction(action);

        if (oldObj != null) {
            historicalEvent.setObjectId(oldObj.getId().toString());
        } else if (newObj != null) {
            historicalEvent.setObjectId(newObj.getId().toString());
        }

        return historicalEvent;
    }

    @Override
    public <T extends BaseEntity> void logHistoricalEvent(T oldHistory, T newHistory, Long timestamp, HistoricalActionEnum action) {
        if (oldHistory == null && newHistory == null) {
            log.warn("[HistoricalEvent] Old and new obj are null!");
            return;
        }
        HistoricalEvent<T> tHistoricalEvent = buildHistoricalEvent(oldHistory, newHistory, timestamp, action);
        T sampleObj = Optional.ofNullable(oldHistory).orElse(newHistory);
        boolean insideTransaction = TransactionSynchronizationManager.isActualTransactionActive();
        if (insideTransaction) {
            triggerSendHistoricalEventAfterTransaction();
            historicalEventThreadLocal.get().computeIfAbsent(sampleObj.getClass(), k -> new ArrayList<>())
                    .add(tHistoricalEvent);
        } else {
            log.info("[HistoricalEvent] Historical for {} processed", sampleObj.getClass().getSimpleName());
            _logHistoricalEvent(tHistoricalEvent);
        }

    }

    private void triggerSendHistoricalEventAfterTransaction() {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(txSynchronization);
        }
    }

    private <T extends BaseEntity> void _logHistoricalEvent(HistoricalEvent<T> tHistoricalEvent) {
        T oldHistory = tHistoricalEvent.getOldData();
        T newHistory = tHistoricalEvent.getData();

        try (var p = new Profiler(getClass(), "logHistoricalEvent")) {
            T sampleObj = Optional.ofNullable(oldHistory).orElse(newHistory);
            String messageKey = null;
            String targetTopic = DEFAULT_HISTORICAL_EVENT_TOPIC;

            // TODO: Add more types of entity here
            if (sampleObj instanceof PaymentHistory) {
                targetTopic = paymentHistoryTopic;
                messageKey = ((PaymentHistory) sampleObj).getPaymentId();
            }

            kafkaProducer.sendMessage(targetTopic, messageKey, JsonUtils.toString(tHistoricalEvent));
        } catch (Exception e) {
            log.error("Error while logHistoricalEvent({})", JsonUtils.toString(tHistoricalEvent), e);
        }
    }
}
