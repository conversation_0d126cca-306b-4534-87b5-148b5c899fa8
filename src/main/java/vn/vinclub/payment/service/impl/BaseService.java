package vn.vinclub.payment.service.impl;

import lombok.extern.slf4j.Slf4j;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.AuthzCustomerDto;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.security.impl.VClubCustomerAuthenticationFilter;
import vn.vinclub.payment.util.ServiceResponse;

/**
 * <AUTHOR> 12/19/24 14:40
 */
@Slf4j
public abstract class BaseService {

    public AuthzCustomerDto getCurrVClubCustomer() {
        AuthzCustomerDto userDto = null;
        try {
            Object object = VClubCustomerAuthenticationFilter.getHttpServletRequest().getSession().getAttribute(AppConst.VCLUB_CUSTOMER);
            userDto = (AuthzCustomerDto) object;
        } catch (Exception e) {
            log.warn(e.getLocalizedMessage());
        }
        if (userDto == null) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.USER_UNAUTHORIZED));
        }

        return userDto;
    }

    public String getCurrVClubCustomerLang() {
        try {
            return getCurrVClubCustomer().getLang();
        } catch (Exception e) {
            log.error("getCurrVClubCustomerLang: {}", e.getMessage());
            return AppConst.DEFAULT_LANGUAGE;
        }
    }

}
