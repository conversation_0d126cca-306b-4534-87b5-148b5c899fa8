package vn.vinclub.payment.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vn.vinclub.payment.constant.AppConst;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.interceptor.RateLimiter;
import vn.vinclub.payment.service.RateLimitService;
import vn.vinclub.payment.util.ServiceResponse;

@Slf4j
@Aspect
@Service
@RequiredArgsConstructor
public class RateLimitServiceImpl extends BaseService implements RateLimitService {

    private final RedissonClient redissonClient;

    @Around("@annotation(rateLimiter)")
    public Object enforceRateLimit(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        StringBuilder key = new StringBuilder(AppConst.REDIS_LOCK.RATE_LIMIT + ":" + rateLimiter.value());
        long rate = rateLimiter.rate();
        Duration duration = Duration.parse(rateLimiter.timeWindow());

        for (RateLimiter.Subject subject : rateLimiter.subjects()) {
            if (subject == null) continue;
            switch (subject) {
                case CUSTOMER ->
                        key.append(":").append(getCurrVClubCustomer().getId());
                case API -> {
                    String apiName = joinPoint.getTarget().getClass().getSimpleName() + "_" + joinPoint.getSignature().getName();
                    key.append(":").append(apiName);
                }
                default -> log.warn("Unknown subject: {}", subject);
            }
        }

        RRateLimiter limiter = redissonClient.getRateLimiter(key.toString());
        limiter.trySetRate(RateType.OVERALL, rate, duration);

        // Check if a permit is available
        if (!limiter.tryAcquire(1)) {
            log.warn("enforceRateLimit: Rate limit reached for {}.", key);
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.RATE_LIMIT_EXCEED_LIMIT));
        }

        // Proceed with the method execution if within limits
        return joinPoint.proceed();
    }

    // It acquires the rate limiter key at most rate times in a sliding window of duration
    @Override
    public boolean tryAcquire(String key, long rate, Duration duration) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        rateLimiter.trySetRate(RateType.OVERALL, rate, duration);

        return rateLimiter.tryAcquire(1);
    }

    @Override
    public void acquire(String key, long rate, Duration duration) {
        boolean acquired = tryAcquire(key, rate, duration);
        if (!acquired) {
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.RATE_LIMIT_EXCEED_LIMIT));
        }
    }

}
