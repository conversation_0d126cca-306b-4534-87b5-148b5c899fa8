package vn.vinclub.payment.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vinclub.payment.enums.InvoiceProviderEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class InvoiceProviderFactory {
    private final Map<InvoiceProviderEnum, InvoiceProviderService> providerMap = new HashMap<>();

    @Autowired
    public InvoiceProviderFactory(List<InvoiceProviderService> providers) {
        for (InvoiceProviderService provider : providers) {
            providerMap.put(provider.getProvider(), provider);
        }
    }

    public InvoiceProviderService getProvider(InvoiceProviderEnum providerName) {
        InvoiceProviderService provider = providerMap.get(providerName);
        if (provider == null) {
            throw new IllegalArgumentException("No provider found for name: " + providerName);
        }
        return provider;
    }

}
