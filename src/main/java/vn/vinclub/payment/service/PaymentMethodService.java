package vn.vinclub.payment.service;

import java.util.List;
import java.util.Optional;

import vn.vinclub.payment.controller.request.CreateUpdatePaymentMethodDto;
import vn.vinclub.payment.model.PaymentMethod;

public interface PaymentMethodService {

    List<PaymentMethod> getActivePaymentMethods();

    PaymentMethod getByIdAndActive(Long paymentMethodId);

    Optional<PaymentMethod> optByIdAndActive(Long paymentMethodId);

    Optional<PaymentMethod> optById(Long paymentMethodId);

    List<PaymentMethod> findByIds(List<Long> paymentMethodIds);

    PaymentMethod updatePaymentMethod(Long id, CreateUpdatePaymentMethodDto updateReq);
}
