package vn.vinclub.payment.service;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.dto.PageDto;
import vn.vinclub.payment.dto.internal.CoreCustomerDto;
import vn.vinclub.payment.dto.internal.CoreCustomerIdentifyRequest;
import vn.vinclub.payment.dto.internal.CoreCustomerIdentityDocumentFilterDto;
import vn.vinclub.payment.dto.internal.CoreCustomerIdentityVerificationDto;
import vn.vinclub.payment.exception.BadRequestException;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.util.ServiceResponse;

import java.net.URISyntaxException;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class InternalService {

    private final RestTemplate restTemplate;
    private final BaseJsonUtils defaultBaseJsonUtils;

    @Value("${core.service.url}")
    private String coreServiceUrl;

    @Value("${core.service.auth}")
    private String coreServiceAuth;

    private String getCoreServiceInternalUrl() {
        return coreServiceUrl + " /internal";
    }

    private HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.put("Authorization", Collections.singletonList(coreServiceAuth)); // auth from app-mw

        return headers;
    }

    public Optional<CoreCustomerDto> optCustomer(CoreCustomerIdentifyRequest req) {
        if (req == null || req.isNotHaveIdentityToSearch()) {
            log.warn("CustomerIdentifyRequest is null");
            return Optional.empty();
        }

        try (Profiler p = new Profiler(getClass(), "optCustomer")) {
            // Check cache
//            Optional<CoreCustomerDto> cachedCustomer = mapCustomerByIdentifyRequest.getIfPresent(req);
//            if (cachedCustomer != null) {
//                try (var p1 = new Profiler(p, "cacheHit")) {
//                    return cachedCustomer;
//                }
//            }

            // Cache miss, fetch from service
            try (var p1 = new Profiler(p, "cacheMiss")) {
                Optional<CoreCustomerDto> customer = internalOptCustomer(req);
                // Cache the result even if it's empty (Optional.empty()) to avoid repetitive calls
//                mapCustomerByIdentifyRequest.put(req, customer);
                return customer;
            }
        }
    }

    private Optional<CoreCustomerDto> internalOptCustomer(CoreCustomerIdentifyRequest req) {
        ResponseEntity<String> exchangeResult = null;

        try (Profiler p = new Profiler(getClass(), "internalOptCustomer")) {
            String serviceUrl = getCoreServiceInternalUrl() + "/customers/by_identify_request";
            HttpEntity<Object> httpEntity = new HttpEntity<>(req, getDefaultHeaders());
            exchangeResult = restTemplate.exchange(serviceUrl, HttpMethod.POST, httpEntity, String.class);

            var result = defaultBaseJsonUtils.toObjectOrThrow(exchangeResult.getBody(), new TypeReference<ServiceResponse<CoreCustomerDto>>() {
            });
            if (result.getCode() == 4019236) {
                return Optional.empty();
            }

            return Optional.ofNullable(result.getData());

        } catch (HttpStatusCodeException e) {
            var responseBodyAs = e.getResponseBodyAs(ServiceResponse.class);
            if (responseBodyAs != null && responseBodyAs.getCode() == 4019236) {
                return Optional.empty();
            }
            log.error("internalOptCustomer({}): {}", defaultBaseJsonUtils.toString(req),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);

            throw new BadRequestException(responseBodyAs);
        } catch (Exception e) {
            log.error("internalOptCustomer({}): {}", defaultBaseJsonUtils.toString(req),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);
            throw new BadRequestException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    public void checkTopupPointPaymentBlacklist(Long customerId) {
        ResponseEntity<String> exchangeResult = null;

        try (Profiler p = new Profiler(getClass(), "checkTopupPointPaymentBlacklist")) {
            String url = UriComponentsBuilder.fromHttpUrl(coreServiceUrl)
                    .path("/internal/features/blacklist/check")
                    .queryParam("customerId", customerId)
                    .queryParam("feature", "TOPUP_POINT_PAYMENT")
                    .toUriString();
            HttpEntity<Object> httpEntity = new HttpEntity<>(url, getDefaultHeaders());

            restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);

        } catch (HttpStatusCodeException e) {
            var responseBodyAs = e.getResponseBodyAs(ServiceResponse.class);
            log.error("checkTopupPointPaymentBlacklist({}): {}", customerId,
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);

            throw new BadRequestException(responseBodyAs);
        } catch (Exception e) {
            log.error("checkTopupPointPaymentBlacklist({}): {}", customerId,
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);
            throw new BadRequestException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }


    public List<CoreCustomerIdentityVerificationDto> findCustomerIdentityVerificationByFilter(CoreCustomerIdentityDocumentFilterDto filter, Pageable pageable) {
        ResponseEntity<String> exchangeResult = null;

        try (Profiler p = new Profiler(getClass(), "findCustomerIdentityVerificationByFilter")) {
            String url = UriComponentsBuilder.fromHttpUrl(coreServiceUrl)
                    .path("/internal/identity-documents")
                    .queryParamIfPresent("customerId", Optional.of(filter.getCustomerId()))
                    .queryParamIfPresent("approvalStatus", Optional.of(filter.getApprovalStatus()))
                    .toUriString();
            HttpEntity<Object> httpEntity = new HttpEntity<>(url, getDefaultHeaders());

            exchangeResult = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);

            var result = defaultBaseJsonUtils.toObjectOrThrow(exchangeResult.getBody(), new TypeReference<ServiceResponse<PageDto<CoreCustomerIdentityVerificationDto>>>() {
            });
            if (result.getData() == null) {
                return Collections.emptyList();
            }

            return result.getData().getData();

        } catch (HttpStatusCodeException e) {
            var responseBodyAs = e.getResponseBodyAs(ServiceResponse.class);
            log.error("internalOptCustomer({}): {}", defaultBaseJsonUtils.toString(filter),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);

            throw new BadRequestException(responseBodyAs);
        } catch (Exception e) {
            log.error("internalOptCustomer({}): {}", defaultBaseJsonUtils.toString(filter),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""), e);
            throw new BadRequestException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }


    public Map<String, URL> getSignedUrlInternalPath(String fileName, boolean isPublic) {
        ResponseEntity<String> exchangeResult = null;
        try {
            String url = UriComponentsBuilder.fromHttpUrl(coreServiceUrl)
                    .replacePath("/r/storages/signed-url/internal")
                    .queryParam("fileName", fileName)
                    .queryParam("isPublic", isPublic)
                    .toUriString();

            HttpEntity<Object> httpEntity = new HttpEntity<>(getDefaultHeaders());
            exchangeResult = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return JsonUtils.toObject(exchangeResult.getBody(), new TypeReference<ServiceResponse<Map<String, URL>>>() {
            }).getData();
        } catch (HttpStatusCodeException e) {
            throw new BusinessLogicException(e.getResponseBodyAs(ServiceResponse.class));
        } catch (Exception e) {
            log.error("Failed when getSignedUrlInternalPath({})\tcode={}, body={}",
                    JsonUtils.toString(fileName),
                    Optional.ofNullable(exchangeResult).map(i -> i.getStatusCode().toString()).orElse(""),
                    Optional.ofNullable(exchangeResult).map(HttpEntity::getBody).orElse(""),
                    e
            );
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }


    public void uploadFileByPresignedUrl(URL presignedUrl, byte[] fileContent) {
        ResponseEntity<String> response = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            HttpEntity<byte[]> requestEntity = new HttpEntity<>(fileContent, headers);

            response = restTemplate.exchange(
                    presignedUrl.toURI(),
                    HttpMethod.PUT,
                    requestEntity,
                    String.class
            );
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("Failed when uploadFileByPresignedUrl({})\tcode={}, body={}",
                    JsonUtils.toString(presignedUrl),
                    Optional.ofNullable(response).map(i -> i.getStatusCode().toString()).orElse(""),
                    Optional.ofNullable(response).map(HttpEntity::getBody).orElse(""),
                    e
            );
            throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
