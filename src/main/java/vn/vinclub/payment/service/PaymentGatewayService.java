package vn.vinclub.payment.service;

import vn.vinclub.payment.dto.PayGateCheckoutResult;
import vn.vinclub.payment.dto.PayGateIpnData;
import vn.vinclub.payment.dto.PayGatePaymentResult;
import vn.vinclub.payment.enums.PaymentGatewayEnum;
import vn.vinclub.payment.model.PaymentHistory;

/**
 * <AUTHOR> 12/11/24 17:35
 */
public interface PaymentGatewayService {

    PaymentGatewayEnum getPayGateType();

    PayGateCheckoutResult checkout(PaymentHistory paymentHistory);

    PayGatePaymentResult getTransactionByPgTxnId(String pgTxnId);

    PayGatePaymentResult processIPN(PayGateIpnData ipnData);

    boolean isValidIPN(PayGateIpnData ipnData);

}
