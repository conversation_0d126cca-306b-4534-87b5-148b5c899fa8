package vn.vinclub.payment.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.payment.constant.AppConst;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@RequiredArgsConstructor
@Slf4j
@Service
public class QuotaServiceImpl implements QuotaService {

    private final RedissonClient redissonClient;

    @Override
    public boolean tryIncreaseQuotaWithLimit(String subKey, long delta, long max, Supplier<Long> loadCurrentFromDbFunc, long ttlSeconds) {
        try (Profiler p = new Profiler(getClass(), "tryIncreaseQuotaWithLimit", subKey)) {

        }
        String key = String.format(AppConst.REDIS_KEY.QUOTA_PREFIX, subKey);
        RBucket<Long> bucket = redissonClient.getBucket(key, LongCodec.INSTANCE);
        Long currentValue = bucket.get();

        if (currentValue == null) {
            try (Profiler p = new Profiler(getClass(), "tryIncreaseQuotaWithLimit", subKey, "cacheMiss")) {
                String lockKey = key + ":lock";
                RLock lock = redissonClient.getLock(lockKey);
                boolean locked = false;

                try {
                    locked = lock.tryLock(5, 2, TimeUnit.SECONDS);
                    if (!locked) return false;

                    currentValue = bucket.get();
                    if (currentValue == null) {
                        Long initialValue = loadCurrentFromDbFunc.get();
                        if (initialValue + delta > max) return false;

                        bucket.set(initialValue + delta, Duration.ofSeconds(ttlSeconds));
                        return true;
                    } else {
                        return tryIncreaseAtomic(key, max, delta);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                } finally {
                    if (locked && lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        } else {
            try (Profiler p = new Profiler(getClass(), "tryIncreaseQuotaWithLimit", subKey, "cacheHit")) {
                return tryIncreaseAtomic(key, max, delta);
            }
        }
    }

    private boolean tryIncreaseAtomic(String key, long maxRequest, long delta) {
        String luaScript = ""
                + "local current = redis.call('get', KEYS[1])\n"
                + "if not current then return -1 end\n"
                + "local new_value = tonumber(current) + tonumber(ARGV[2])\n"
                + "if new_value > tonumber(ARGV[1]) then return 0 end\n"
                + "redis.call('incrby', KEYS[1], tonumber(ARGV[2]))\n"
                + "return 1";

        RScript script = redissonClient.getScript();
        Number result = script.eval(
                RScript.Mode.READ_WRITE,
                luaScript,
                RScript.ReturnType.INTEGER,
                Collections.singletonList(key),
                maxRequest, delta
        );

        return result != null && result.intValue() == 1;
    }

}
