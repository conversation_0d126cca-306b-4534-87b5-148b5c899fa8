package vn.vinclub.payment.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.payment.controller.request.PaymentHistoryFilter;
import vn.vinclub.payment.controller.request.customer.CheckoutPaymentRequest;
import vn.vinclub.payment.controller.request.customer.GetPaymentHistoryRequest;
import vn.vinclub.payment.controller.request.customer.PaymentMethodRequest;
import vn.vinclub.payment.dto.CheckoutPreviewDto;
import vn.vinclub.payment.dto.CustomerPaymentConfigDto;
import vn.vinclub.payment.dto.FullPaymentHistoryDto;
import vn.vinclub.payment.dto.PaymentHistoryDto;
import vn.vinclub.payment.dto.PaymentMethodDto;
import vn.vinclub.payment.enums.InvoiceSourceEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;

import java.util.List;

/**
 * <AUTHOR> 12/11/24 17:36
 */
public interface PaymentService {

    CheckoutPreviewDto calculateCheckout(CheckoutPaymentRequest checkoutPaymentRequest);

    PaymentHistoryDto checkout(CheckoutPaymentRequest checkoutPaymentRequest);

    List<PaymentMethodDto> getAvailablePaymentMethods(PaymentMethodRequest paymentMethodRequest);

    Page<PaymentHistoryDto> getPaymentHistories(GetPaymentHistoryRequest req, Pageable pageable);

    PaymentHistoryDto getPaymentHistoryByPaymentId(String transactionId);

    Page<FullPaymentHistoryDto> getFullPaymentHistories(PaymentHistoryFilter filter, Pageable pageable);

    void syncPaymentStatusFromPaymentGateway(long gtId);

    FullPaymentHistoryDto getFullPaymentHistoryByPaymentId(String paymentId);

    CustomerPaymentConfigDto getConfigs(Long customerId);

    void validatePaymentQuota(TransactionTypeEnum transactionTypeEnum, Long customerId);

    void createDraftPaymentInvoice(String paymentId, InvoiceSourceEnum invoiceSource);
}
