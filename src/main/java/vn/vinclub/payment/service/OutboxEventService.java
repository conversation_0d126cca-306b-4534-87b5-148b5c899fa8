package vn.vinclub.payment.service;


import vn.vinclub.payment.dto.event.outbox.BaseOutboxEvent;
import vn.vinclub.payment.model.OutboxEvent;

import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
public interface OutboxEventService {

    <T extends BaseOutboxEvent> void create(T event, String outboxEventId);

    void processPendingOutbox();

    void makeSent(String outboxEventId, OutboxEvent outboxEvent) throws ExecutionException, InterruptedException;

}
