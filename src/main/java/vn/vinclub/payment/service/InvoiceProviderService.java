package vn.vinclub.payment.service;

import vn.vinclub.payment.dto.invoice.CheckInvoiceResult;
import vn.vinclub.payment.dto.invoice.DownloadInvoiceResponse;
import vn.vinclub.payment.dto.invoice.IssueInvoiceResult;
import vn.vinclub.payment.enums.InvoiceProviderEnum;
import vn.vinclub.payment.model.Invoice;

public interface InvoiceProviderService {

    InvoiceProviderEnum getProvider();

    String getProviderUniqueId(Invoice invoice);

    IssueInvoiceResult issueInvoice(Invoice invoice);

    CheckInvoiceResult checkInvoice(Invoice invoice);

    DownloadInvoiceResponse downloadInvoice(Invoice invoice);
}
