package vn.vinclub.payment.service;

import vn.vinclub.payment.dto.event.historical.HistoricalActionEnum;
import vn.vinclub.payment.model.BaseEntity;

public interface LogHistoricalService {
    <T extends BaseEntity> void logHistoricalEvent(T oldObj, T newObj, Long timestamp, HistoricalActionEnum action);
    default <T extends BaseEntity> void logHistoricalEvent(T oldObj, T newObj) {
        logHistoricalEvent(oldObj, newObj, null, null);
    }

}
