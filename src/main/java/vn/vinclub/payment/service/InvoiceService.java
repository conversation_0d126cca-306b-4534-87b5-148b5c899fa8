package vn.vinclub.payment.service;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.payment.dto.InvoiceFilter;
import vn.vinclub.payment.dto.event.outbox.IssueInvoiceEvent;
import vn.vinclub.payment.dto.invoice.CreateInvoiceRequest;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfo;
import vn.vinclub.payment.dto.invoice.InvoiceBuyerInfoConfigDto;
import vn.vinclub.payment.dto.invoice.InvoiceRequestEvent;
import vn.vinclub.payment.model.Invoice;

import java.net.URL;
import java.util.List;

public interface InvoiceService {

    List<InvoiceBuyerInfo> getRecentInvoiceBuyerInfos(Long customerId);

    InvoiceBuyerInfoConfigDto getInvoiceBuyerConfigs(Long customerId);

    InvoiceBuyerInfo validateInvoiceBuyerInfo(Long customerId, InvoiceBuyerInfo invoiceBuyerInfo);

    void handleInvoiceRequestEvent(InvoiceRequestEvent event);

    Invoice createDraftInvoice(CreateInvoiceRequest request);

    void processIssueInvoice(Long invoiceId);

    void issueInvoiceByEvent(IssueInvoiceEvent event);

    Page<Invoice> getAllInvoicesByFilter(InvoiceFilter filter, Pageable pageable);

    void scanAutoInvoiceToIssue();

    URL downloadInvoice(Long invoiceId);
}
