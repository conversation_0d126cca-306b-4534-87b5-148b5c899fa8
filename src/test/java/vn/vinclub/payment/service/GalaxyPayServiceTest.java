package vn.vinclub.payment.service;

import com.fasterxml.uuid.Generators;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.payment.constant.AppErrorCode;
import vn.vinclub.payment.enums.PaymentMethodEnum;
import vn.vinclub.payment.enums.PaymentServiceProviderEnum;
import vn.vinclub.payment.enums.TransactionTypeEnum;
import vn.vinclub.payment.exception.BusinessLogicException;
import vn.vinclub.payment.model.PaymentHistory;
import vn.vinclub.payment.service.impl.GalaxyPayServiceImpl;

/**
 * <AUTHOR> 12/16/24 18:04
 */
@SpringBootTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class GalaxyPayServiceTest {

    @Autowired
    private GalaxyPayServiceImpl _galaxyPayService;

    @Test
    public void testPay() {

        try {

            PaymentHistory paymentHistory = new PaymentHistory();
            paymentHistory.setPaymentId(Generators.timeBasedEpochGenerator().generate().toString());
            paymentHistory.setPaymentDate(LocalDateTime.now());
            paymentHistory.setPaymentMethodType(PaymentMethodEnum.QR_PAYMENT);
            paymentHistory.setServiceProviderType(PaymentServiceProviderEnum.VIETQR);
            paymentHistory.setTransactionType(TransactionTypeEnum.TOPUP_POINT);
            paymentHistory.setAmount(BigDecimal.valueOf(1000000));
            paymentHistory.setTotalAmount(BigDecimal.valueOf(1000000));

            var result = _galaxyPayService.checkout(paymentHistory);
            System.out.println(result.getCheckoutUrl());
//        System.out.println(JsonUtils.toString(result));
        } catch (BusinessLogicException e) {
            if (e.getPayload().getCode() == AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR.getCode()) {
                return;
            }
            throw e;
        }
    }

    @Test
    public void testGetTxn() {

        try {
            //        var id = "2412175322544011443555829";
//        var id = "2412175101298453378723922";
//        var id = "2412175715886373619389872";
//        var id = "2412175085942144218539603";
            var id = "2412175739995823568450706";

            var result = _galaxyPayService.getTransactionByPgTxnId(id);
            System.out.println(JsonUtils.toSnakeCaseStringPretty(result));
        } catch (BusinessLogicException e) {
            if (e.getPayload().getCode() == AppErrorCode.PAYMENT_GATEWAY_INTERNAL_SERVER_ERROR.getCode()) {
                return;
            }
            throw e;
        }

//        {
//            "txn_id" : "cfe5b4a9-cc1c-4008-b45f-cfb571faab1d",
//                "transaction_date" : "2024-12-17T15:28:03",
//                "description" : "Secure Page Demo",
//                "amount" : 10000.0,
//                "currency" : "VND",
//                "pgw_txn_id" : "2412175715886373619389872",
//                "pgw_txn_stage" : "SUCCESSFUL",
//                "pgw_txn_status" : "200",
//                "pgw_txn_status_message" : "Transaction successful."
//        }
    }

    @Test
    public void testParseCallbackData() {

        var s = """
                {
                  "data": "eyJyZXF1ZXN0SUQiOiIwMTkzZDJmZS00MWJjLTc0NGItOTM5OC1mNzBhOTA3YWI2YjgiLCJyZXNwb25zZURhdGVUaW1lIjoiMjAyNDEyMTcxMjAyMDAiLCJyZXNwb25zZUNvZGUiOiI0MDAiLCJyZXNwb25zZU1lc3NhZ2UiOiJRdcO9IGtow6FjaCDEkcOjIGjhu6d5IGdpYW8gZOG7i2NoLiBWdWkgbMOybmcga2nhu4NtIHRyYSB2w6AgdGjhu60gbOG6oWkuIiwicmVzcG9uc2VEYXRhIjp7InRyYW5zYWN0aW9uSUQiOiIyNDEyMTc1MTAxMjk4NDUzMzc4NzIzOTIyIiwib3JkZXJJRCI6IjAxOTNkMmZlLTQxYjktNzc0YS1hMTJiLWU1ZDZlZGZkOTc0ZCIsIm9yZGVyTnVtYmVyIjoiIiwib3JkZXJBbW91bnQiOiIxMDAwMDAwIiwib3JkZXJEZXNjcmlwdGlvbiI6IlRoYW5oIHRvw6FuIGNobyDEkcahbiBow6BuZyBHYWxheHkgUGF5Iiwib3JkZXJDdXJyZW5jeSI6IlZORCIsIm9yZGVyRGF0ZVRpbWUiOiIyMDI0MTIxNzEyMDEzOCIsImV4dHJhRGF0YSI6Int9IiwibGFuZ3VhZ2UiOiJ2aSJ9fQ==",
                  "signature": "7ce171447f3696983dede04415924289b18ece0018d61375db710e30534790fd"
                }""";

        var node = JsonUtils.stringToJsonNode(s);
        Assertions.assertEquals(
                node.path("signature").asText(),
                _galaxyPayService.createSignature(node.path("data").asText())
        );

    }

    @Test
    public void createSignature() {
        var s = """
                {
                    "requestID": "14653fa1-2f69-4f4f-9d4b-240e1bebbdf3",
                    "requestDateTime": 20220426083322,
                    "requestData": {
                        "apiOperation": "PAY",
                        "orderID": "2a43e4b9-c5df-49e6-8b18-42af9bcd150e",
                        "orderNumber": 205249008064,
                        "orderAmount": 100000,
                        "orderCurrency": "VND",
                        "orderDateTime": 20220426083322,
                        "orderDescription": "DEMO TRANSACTION",
                        "paymentMethod": "DOMESTIC",
                        "sourceType": "970400",
                        "language": "vi"
                    }
                }""";

        System.out.println(_galaxyPayService.createSignature(JsonUtils.toString(JsonUtils.stringToJsonNode(s))));

    }

}
