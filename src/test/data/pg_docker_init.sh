#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-<PERSON><PERSON><PERSON>
	create schema if not exists $POSTGRES_SCHEMA;

  CREATE TABLE IF NOT EXISTS payment_db.payment_gateway
  (
      id bigserial NOT NULL,
      created_by character varying(100),
      created_on timestamptz,
      updated_by character varying(100),
      updated_on timestamptz DEFAULT CURRENT_TIMESTAMP,
      "version" int4 DEFAULT '1',
      active boolean NOT NULL DEFAULT true,
      deleted_on timestamptz,
      display_names jsonb,
      display_order integer,
      logo jsonb,
      name character varying(255),
      pgw_type character varying(30),
      PRIMARY KEY (id)
  );

  CREATE TABLE IF NOT EXISTS payment_db.payment_service_provider
  (
      id bigserial NOT NULL,
      created_by character varying(100),
      created_on timestamptz,
      updated_by character varying(100),
      updated_on timestamptz DEFAULT CURRENT_TIMESTAMP,
      "version" int4 DEFAULT '1',
      active boolean NOT NULL DEFAULT true,
      deleted_on timestamptz,
      display_names jsonb,
      display_order integer,
      logo jsonb,
      name character varying(255),
      payment_method character varying(30),
      payment_method_provider character varying(30),
      PRIMARY KEY (id)
  );

  CREATE TABLE IF NOT EXISTS payment_db.payment_method
  (
      id bigserial NOT NULL,
      created_by character varying(100),
      created_on timestamptz,
      updated_by character varying(100),
      updated_on timestamptz DEFAULT CURRENT_TIMESTAMP,
      version integer DEFAULT '1',
      active boolean NOT NULL DEFAULT true,
      deleted_on timestamptz,
      display_names jsonb,
      display_order integer,
      fee_percentage numeric(19,2) DEFAULT 0.00,
      fee_required boolean default false,
      fixed_fee numeric(19,2) DEFAULT 0.00,
      max_amount_allowed numeric(19,2) NOT NULL,
      min_amount_allowed numeric(19,2) NOT NULL,
      payment_method_type character varying(30),
      pgw_id bigint,
      pgw_type character varying(30),
      service_provider_id bigint,
      service_provider_type character varying(30),
      status character varying(30) DEFAULT 'ACTIVE',
      PRIMARY KEY (id)
  );

  CREATE TABLE IF NOT EXISTS payment_db.payment_history
  (
      id bigserial NOT NULL,
      created_by character varying(100),
      created_on timestamptz,
      updated_by character varying(100),
      updated_on timestamptz default CURRENT_TIMESTAMP,
      version integer default 1,
      active boolean NOT NULL DEFAULT true,
      amount numeric(19,2),
      currency character varying(10),
      customer_id bigint NOT NULL,
      data_request jsonb,
      deleted_on timestamptz,
      description character varying(255),
      fee_amount numeric(19,2),
      original_payment_id character varying(64),
      payment_date timestamptz,
      payment_id character varying(64) NOT NULL,
      payment_method_id bigint,
      payment_method_type character varying(30),
      pgw_id bigint,
      pgw_txn_id character varying(100),
      pgw_txn_response jsonb,
      pgw_txn_response_code character varying(100),
      pgw_type character varying(30),
      is_retry boolean default false,
      service_provider_id bigint,
      service_provider_type character varying(30),
      status character varying(20),
      transaction_type character varying(30),
      discount_amount numeric(19,2),
      total_amount numeric(19,2),
      PRIMARY KEY (id)
  );

  CREATE INDEX IF NOT EXISTS payment_history_idx_payment_id ON payment_db.payment_history USING btree (payment_id);
  CREATE INDEX IF NOT EXISTS payment_history_idx_original_payment_id ON payment_db.payment_history USING btree (original_payment_id);
  CREATE INDEX IF NOT EXISTS payment_history_idx_status_customer_id ON payment_db.payment_history USING btree (status, customer_id);
  CREATE INDEX IF NOT EXISTS payment_history_idx_payment_date ON payment_db.payment_history USING btree (payment_date);
  CREATE INDEX IF NOT EXISTS payment_history_idx_status_pmt_spt_pgwt ON payment_db.payment_history (status, payment_method_type, service_provider_type, pgw_type);

  CREATE TABLE IF NOT EXISTS payment_db.payment_outbox_event
  (
      id bigserial NOT NULL,
      created_by character varying(100),
      created_on timestamptz,
      updated_by character varying(100),
      updated_on timestamptz default CURRENT_TIMESTAMP,
      version integer default 1,
      event_id character varying(100),
      payload jsonb,
      status character varying(30),
      payment_id character varying(100),
      payment_status character varying(30),
      transaction_type character varying(30),
      PRIMARY KEY (id)
  );

  INSERT INTO payment_db.payment_gateway ( id, created_by, created_on, updated_by, updated_on, version, active, deleted_on, display_names, display_order, logo, name, pgw_type)
  VALUES
  ( 1, 'SYSTEM', '2024-12-20 00:00:00+07', 'SYSTEM', '2024-12-20 00:00:00+07', 1, true, NULL, '{"en": "Galaxy Pay", "vi": "Galaxy Pay"}', 1, '{"vi": "", "en": ""}', 'Galaxy Pay', 'GALAXY_PAY')
  ON CONFLICT DO NOTHING;

  INSERT INTO payment_db.payment_service_provider ( id, active, payment_method, payment_method_provider, name, display_names, logo, display_order, created_on, updated_on, created_by, updated_by)
  VALUES
  (1, TRUE, 'E_WALLET', 'MOMO', 'MOMO Payment', '{"en": "MOMO", "vi": "MOMO"}', '{"vi": {"url": ""}}', 1, '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 'SYSTEM', 'SYSTEM'),
  (2, TRUE, 'E_WALLET', 'ZALOPAY', 'ZALOPAY Payment', '{"en": "ZALOPAY", "vi": "ZALOPAY"}', '{"vi": {"url": ""}}', 2, '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 'SYSTEM', 'SYSTEM'),
  (3, true, 'QR_PAYMENT',	'VIETQR', 'VIET QR', '{"en": "VIET QR", "vi": "VIET QR"}', '{"vi": {"url": ""}}', 3, '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 'SYSTEM', 'SYSTEM')
  ON CONFLICT DO NOTHING;

  INSERT INTO payment_db.payment_method ( id, active, deleted_on, payment_method_type, service_provider_type, service_provider_id, pgw_type, pgw_id, display_order, display_names, created_on, updated_on, min_amount_allowed, max_amount_allowed, created_by, updated_by)
  VALUES
  (1, false, NULL, 'E_WALLET', 'MOMO', 1, 'GALAXY_PAY', 1, 1, '{"en": "Momo", "vi": "MOMO"}', '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 1000, 100000000, 'SYSTEM', 'SYSTEM'),
  (2, false, NULL, 'E_WALLET', 'ZALOPAY', 1, 'GALAXY_PAY', 1, 2, '{"en": "Zalopay", "vi": "ZALOPAY"}', '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 1000, 100000000, 'SYSTEM', 'SYSTEM'),
  (3, true, NULL, 'QR_PAYMENT', 'VIETQR', 3, 'GALAXY_PAY', 1, 4, '{"en": "VietQR", "vi": "VietQR"}', '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 1000, 100000000, 'SYSTEM', 'SYSTEM'),
  (4, false, NULL, 'DOMESTIC_CARD', NULL, NULL, 'GALAXY_PAY', 1, 3, '{"en": "Domestic Card (Napas)", "vi": "Thẻ Nội Địa (Napas)"}', '2024-12-20 00:00:00+07', '2024-12-20 00:00:00+07', 1000, 100000000, 'SYSTEM', 'SYSTEM')
  ON CONFLICT DO NOTHING;

  ALTER TABLE payment_db.payment_method ADD COLUMN fee_configs jsonb;

  ALTER TABLE payment_db.payment_history ADD COLUMN fee_details jsonb;
  ALTER TABLE payment_db.payment_history ADD COLUMN invoice_buyer_info jsonb;

  CREATE TABLE IF NOT EXISTS payment_db.customer_invoice_buyer_info
    (
        id                bigserial primary key,
        created_by        varchar(255),
        created_on        timestamp                           not null,
        updated_by        varchar(256),
        updated_on        timestamp default CURRENT_TIMESTAMP not null,
        version           integer,

        customer_id       bigint      not null,
        recent_invoice_buyer_infos   jsonb
    );
    create index customer_invoice_buyer_info_idx_customer_id on payment_db.customer_invoice_buyer_info using hash (customer_id);

EOSQL
