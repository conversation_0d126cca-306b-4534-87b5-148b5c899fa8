#!/bin/bash

# Script to compare Jackson configuration between local and staging environments

echo "=== Jackson Configuration Comparison ==="
echo ""

LOCAL_URL="http://localhost:8080/pm/internal/diagnostic/jackson-config"
STAGING_URL="https://stag-api-admin.vinclub.vn/pm/payment/internal/diagnostic/jackson-config"

echo "Fetching local configuration..."
LOCAL_CONFIG=$(curl -s "$LOCAL_URL" 2>/dev/null || echo "Failed to fetch local config")

echo "Fetching staging configuration..."
STAGING_CONFIG=$(curl -s "$STAGING_URL" 2>/dev/null || echo "Failed to fetch staging config")

echo ""
echo "=== LOCAL CONFIGURATION ==="
echo "$LOCAL_CONFIG" | jq . 2>/dev/null || echo "$LOCAL_CONFIG"

echo ""
echo "=== STAGING CONFIGURATION ==="
echo "$STAGING_CONFIG" | jq . 2>/dev/null || echo "$STAGING_CONFIG"

echo ""
echo "=== COMPARISON ==="
if command -v jq &> /dev/null; then
    echo "Local test number type: $(echo "$LOCAL_CONFIG" | jq -r '.testNumberType // "N/A"')"
    echo "Staging test number type: $(echo "$STAGING_CONFIG" | jq -r '.testNumberType // "N/A"')"
    echo ""
    echo "Local test number value: $(echo "$LOCAL_CONFIG" | jq -r '.testNumberValue // "N/A"')"
    echo "Staging test number value: $(echo "$STAGING_CONFIG" | jq -r '.testNumberValue // "N/A"')"
else
    echo "Install jq for better comparison output"
fi

echo ""
echo "=== ANALYSIS SPECIFIC OUTBOX EVENT ==="
echo "Usage: $0 <eventId>"
if [ ! -z "$1" ]; then
    EVENT_ID="$1"
    echo "Analyzing eventId: $EVENT_ID"
    
    LOCAL_ANALYSIS=$(curl -s "$LOCAL_URL/../outbox-payload-analysis?eventId=$EVENT_ID" 2>/dev/null || echo "Failed")
    STAGING_ANALYSIS=$(curl -s "$STAGING_URL/../outbox-payload-analysis?eventId=$EVENT_ID" 2>/dev/null || echo "Failed")
    
    echo ""
    echo "Local analysis:"
    echo "$LOCAL_ANALYSIS" | jq . 2>/dev/null || echo "$LOCAL_ANALYSIS"
    
    echo ""
    echo "Staging analysis:"
    echo "$STAGING_ANALYSIS" | jq . 2>/dev/null || echo "$STAGING_ANALYSIS"
fi
